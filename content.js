// Content script for Todo Notes Extension
// This script runs on every webpage to provide overlay functionality

(function() {
    'use strict';
    
    let todoOverlay = null;
    let isOverlayVisible = false;
    
    // Initialize content script
    function init() {
        console.log('Todo Notes content script loaded');
        
        // Listen for messages from popup or background
        chrome.runtime.onMessage.addListener(handleMessage);
        
        // Create overlay if needed
        createOverlay();
        
        // Listen for keyboard shortcuts
        document.addEventListener('keydown', handleKeyboardShortcuts);
    }
    
    // Handle messages from other parts of the extension
    function handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'toggleOverlay':
                toggleOverlay();
                sendResponse({ success: true });
                break;
                
            case 'showOverlay':
                showOverlay();
                sendResponse({ success: true });
                break;
                
            case 'hideOverlay':
                hideOverlay();
                sendResponse({ success: true });
                break;
                
            case 'getCurrentUrl':
                sendResponse({ url: window.location.href });
                break;
                
            default:
                console.log('Unknown content script action:', request.action);
        }
    }
    
    // Handle keyboard shortcuts
    function handleKeyboardShortcuts(event) {
        // Ctrl+Shift+T to toggle overlay
        if (event.ctrlKey && event.shiftKey && event.key === 'T') {
            event.preventDefault();
            toggleOverlay();
        }
    }
    
    // Create the overlay element
    function createOverlay() {
        if (todoOverlay) return;
        
        todoOverlay = document.createElement('div');
        todoOverlay.id = 'todo-notes-overlay';
        todoOverlay.innerHTML = `
            <div class="todo-overlay-header">
                <h3>Quick Todo/Notes</h3>
                <button class="close-btn" id="close-overlay">×</button>
            </div>
            <div class="todo-overlay-content">
                <div class="quick-add">
                    <input type="text" id="quick-todo" placeholder="Quick todo...">
                    <button id="add-quick-todo">Add</button>
                </div>
                <div class="quick-notes">
                    <textarea id="quick-note" placeholder="Quick note..."></textarea>
                    <button id="add-quick-note">Add Note</button>
                </div>
                <div class="current-items">
                    <h4>Current Page Items</h4>
                    <div id="overlay-items-list"></div>
                </div>
            </div>
        `;
        
        // Add event listeners
        todoOverlay.querySelector('#close-overlay').addEventListener('click', hideOverlay);
        todoOverlay.querySelector('#add-quick-todo').addEventListener('click', addQuickTodo);
        todoOverlay.querySelector('#add-quick-note').addEventListener('click', addQuickNote);
        
        // Handle Enter key in quick todo input
        todoOverlay.querySelector('#quick-todo').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addQuickTodo();
            }
        });
        
        document.body.appendChild(todoOverlay);
    }
    
    // Toggle overlay visibility
    function toggleOverlay() {
        if (isOverlayVisible) {
            hideOverlay();
        } else {
            showOverlay();
        }
    }
    
    // Show overlay
    function showOverlay() {
        if (!todoOverlay) createOverlay();
        
        todoOverlay.style.display = 'block';
        isOverlayVisible = true;
        
        // Load current page items
        loadCurrentPageItems();
        
        // Focus on quick todo input
        setTimeout(() => {
            todoOverlay.querySelector('#quick-todo').focus();
        }, 100);
    }
    
    // Hide overlay
    function hideOverlay() {
        if (todoOverlay) {
            todoOverlay.style.display = 'none';
            isOverlayVisible = false;
        }
    }
    
    // Add quick todo
    function addQuickTodo() {
        const input = todoOverlay.querySelector('#quick-todo');
        const text = input.value.trim();
        
        if (!text) return;
        
        const todo = {
            id: generateId(),
            text: text,
            completed: false,
            url: window.location.href,
            baseUrl: getBaseUrl(window.location.href),
            createdAt: new Date().toISOString(),
            type: 'todo'
        };
        
        // Save to storage
        chrome.storage.local.get(['todos'], (result) => {
            const todos = result.todos || [];
            todos.unshift(todo); // Add to beginning of array
            chrome.storage.local.set({ todos }, () => {
                input.value = '';
                loadCurrentPageItems();
                console.log('Quick todo added:', todo);
            });
        });
    }
    
    // Add quick note
    function addQuickNote() {
        const textarea = todoOverlay.querySelector('#quick-note');
        const text = textarea.value.trim();
        
        if (!text) return;
        
        const note = {
            id: generateId(),
            title: text.split('\n')[0].substring(0, 50) + (text.length > 50 ? '...' : ''),
            content: text,
            url: window.location.href,
            baseUrl: getBaseUrl(window.location.href),
            createdAt: new Date().toISOString(),
            type: 'note'
        };
        
        // Save to storage
        chrome.storage.local.get(['notes'], (result) => {
            const notes = result.notes || [];
            notes.unshift(note); // Add to beginning of array
            chrome.storage.local.set({ notes }, () => {
                textarea.value = '';
                loadCurrentPageItems();
                console.log('Quick note added:', note);
            });
        });
    }
    
    // Load items for current page
    function loadCurrentPageItems() {
        const currentUrl = window.location.href;
        const itemsList = todoOverlay.querySelector('#overlay-items-list');
        
        Promise.all([
            new Promise(resolve => chrome.storage.local.get(['todos'], resolve)),
            new Promise(resolve => chrome.storage.local.get(['notes'], resolve))
        ]).then(([todosResult, notesResult]) => {
            const todos = todosResult.todos || [];
            const notes = notesResult.notes || [];
            
            const currentTodos = todos.filter(todo => todo.url === currentUrl);
            const currentNotes = notes.filter(note => note.url === currentUrl);
            
            itemsList.innerHTML = '';
            
            if (currentTodos.length === 0 && currentNotes.length === 0) {
                itemsList.innerHTML = '<p class="no-items">No items for this page</p>';
                return;
            }
            
            // Display todos
            currentTodos.forEach(todo => {
                const todoEl = document.createElement('div');
                todoEl.className = 'overlay-item todo-item';
                todoEl.innerHTML = `
                    <input type="checkbox" ${todo.completed ? 'checked' : ''} 
                           onchange="toggleTodoComplete('${todo.id}')">
                    <span class="${todo.completed ? 'completed' : ''}">${todo.text}</span>
                `;
                itemsList.appendChild(todoEl);
            });
            
            // Display notes
            currentNotes.forEach(note => {
                const noteEl = document.createElement('div');
                noteEl.className = 'overlay-item note-item';
                noteEl.innerHTML = `
                    <strong>${note.title}</strong>
                    <p>${note.content.substring(0, 100)}${note.content.length > 100 ? '...' : ''}</p>
                `;
                itemsList.appendChild(noteEl);
            });
        });
    }
    
    // Utility functions
    function generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
    
    function getBaseUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}`;
        } catch (error) {
            return url;
        }
    }
    
    // Make toggle function globally available for inline event handlers
    window.toggleTodoComplete = function(todoId) {
        chrome.storage.local.get(['todos'], (result) => {
            const todos = result.todos || [];
            const todo = todos.find(t => t.id === todoId);
            if (todo) {
                todo.completed = !todo.completed;

                // Add or remove completed date
                if (todo.completed) {
                    todo.completedAt = new Date().toISOString();
                } else {
                    delete todo.completedAt;
                }

                chrome.storage.local.set({ todos }, () => {
                    loadCurrentPageItems();
                });
            }
        });
    };
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
