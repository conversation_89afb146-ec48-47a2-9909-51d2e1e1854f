<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StickyLink - Full Screen</title>
    <link rel="stylesheet" href="popup.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
            background: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        .fullscreen-header {
            background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .fullscreen-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }

        .fullscreen-header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .fullscreen-header-actions button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .fullscreen-header-actions button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .fullscreen-content {
            padding: 20px;
            min-height: calc(100vh - 100px);
            display: flex;
            flex-direction: column;
        }

        .fullscreen-nav {
            display: flex;
            gap: 16px;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 16px;
        }

        .fullscreen-nav button {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: var(--bg-primary);
            color: var(--text-primary);
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .fullscreen-nav button:hover {
            background: var(--bg-secondary);
        }

        .fullscreen-nav button.active {
            background: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        .fullscreen-sections {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .section {
            margin-bottom: 40px;
            flex: 1;
        }

        .section h2 {
            color: var(--text-primary);
            font-size: 20px;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--accent-primary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            font-size: 14px;
        }

        .section-controls select {
            padding: 4px 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 12px;
        }

        /* Vertical Split Layout */
        .vertical-split .fullscreen-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .vertical-split .section {
            margin-bottom: 0;
        }

        .vertical-split .section h2 {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            padding: 16px 0 8px 0;
            margin: 0 0 16px 0;
        }

        /* Horizontal Split Layout (default) */
        .horizontal-split .fullscreen-sections {
            display: flex;
            flex-direction: column;
        }

        .horizontal-split .section {
            margin-bottom: 40px;
        }

        /* Add form styling */
        .add-form {
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }

        .add-form h3 {
            margin: 0 0 16px 0;
            color: var(--text-primary);
            font-size: 16px;
        }

        .form-row {
            display: flex;
            gap: 12px;
            align-items: flex-end;
            margin-bottom: 12px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .fullscreen .input-field,
        .fullscreen .textarea-field {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 14px;
            background: var(--bg-primary);
            color: var(--text-primary);
            box-sizing: border-box;
        }

        .fullscreen .textarea-field {
            min-height: 80px;
            resize: vertical;
        }

        .fullscreen .add-btn {
            padding: 12px 24px;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .fullscreen .add-btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
        }

        .items-container {
            flex: 1;
            overflow-y: auto;
        }
    </style>
</head>
<body class="fullscreen horizontal-split">
    <div class="container">
        <div class="fullscreen-header">
            <h1>📝 StickyLink</h1>
            <div class="fullscreen-header-actions">
                <button id="layout-toggle-btn">Switch to Vertical</button>
                <button id="settings-btn">⚙️ Settings</button>
            </div>
        </div>

        <div class="fullscreen-content">
            <div class="fullscreen-nav">
                <button id="active-tab" class="active">Active Items</button>
                <button id="completed-tab">Completed & Archived</button>
            </div>

            <!-- Active Items View -->
            <div id="active-view" class="fullscreen-sections">
                <!-- Todos Section -->
                <div class="section">
                    <h2>
                        ✅ Todos
                        <div class="section-controls">
                            <label>Group by:</label>
                            <select id="todo-group-by">
                                <option value="none">None</option>
                                <option value="due-date">Due Date</option>
                                <option value="created-date">Created Date</option>
                                <option value="type">Type</option>
                            </select>
                        </div>
                    </h2>

                    <div class="add-form">
                        <h3>Add New Todo</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="todo-input">Todo Text</label>
                                <input type="text" id="todo-input" class="input-field" placeholder="Enter your todo...">
                            </div>
                            <div class="form-group">
                                <label for="todo-due-date">Due Date (Optional)</label>
                                <input type="date" id="todo-due-date" class="input-field">
                            </div>
                            <button id="add-todo-btn" class="add-btn">Add Todo</button>
                        </div>
                    </div>

                    <div id="todos-list" class="items-container"></div>
                </div>

                <!-- Notes Section -->
                <div class="section">
                    <h2>
                        📝 Notes
                        <div class="section-controls">
                            <label>Group by:</label>
                            <select id="note-group-by">
                                <option value="none">None</option>
                                <option value="created-date">Created Date</option>
                                <option value="type">Type</option>
                            </select>
                        </div>
                    </h2>

                    <div class="add-form">
                        <h3>Add New Note</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="note-title">Note Title (Optional)</label>
                                <input type="text" id="note-title" class="input-field" placeholder="Enter note title...">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="note-content">Note Content</label>
                            <textarea id="note-content" class="textarea-field" placeholder="Enter your note content..."></textarea>
                        </div>
                        <div class="form-row" style="margin-top: 12px;">
                            <button id="add-note-btn" class="add-btn">Add Note</button>
                        </div>
                    </div>

                    <div id="notes-list" class="items-container"></div>
                </div>
            </div>

            <!-- Completed & Archived View -->
            <div id="completed-view" class="fullscreen-sections" style="display: none;">
                <!-- Completed Todos Section -->
                <div class="section">
                    <h2>
                        ✅ Completed Todos
                        <div class="section-controls">
                            <label>Group by:</label>
                            <select id="completed-todo-group-by">
                                <option value="none">None</option>
                                <option value="completed-date">Completed Date</option>
                                <option value="due-date">Due Date</option>
                                <option value="created-date">Created Date</option>
                                <option value="type">Type</option>
                            </select>
                        </div>
                    </h2>
                    <div id="completed-todos-list" class="items-container"></div>
                </div>

                <!-- Archived Notes Section -->
                <div class="section">
                    <h2>
                        📝 Archived Notes
                        <div class="section-controls">
                            <label>Group by:</label>
                            <select id="archived-note-group-by">
                                <option value="none">None</option>
                                <option value="completed-date">Archived Date</option>
                                <option value="created-date">Created Date</option>
                                <option value="type">Type</option>
                            </select>
                        </div>
                    </h2>
                    <div id="archived-notes-list" class="items-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Picker Modal -->
    <div id="date-picker-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Set Due Date</h3>
                <button class="close-btn" id="close-date-picker">×</button>
            </div>
            <div class="modal-body">
                <div class="quick-date-options">
                    <button class="quick-date-btn" data-days="0">Today</button>
                    <button class="quick-date-btn" data-days="1">Tomorrow</button>
                    <button class="quick-date-btn" data-end-of-week="true">End of Week</button>
                    <button class="quick-date-btn" data-next-week="true">Next Week</button>
                </div>
                <div class="date-picker-section">
                    <label for="date-picker-input">Or select a specific date:</label>
                    <input type="date" id="date-picker-input" class="date-picker-input">
                </div>
                <div class="date-picker-actions">
                    <button id="set-date-btn" class="add-btn">Set Date</button>
                    <button id="clear-date-btn" class="cancel-btn">Clear Date</button>
                    <button id="cancel-date-btn" class="cancel-btn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- URL Link Modal -->
    <div id="url-link-modal" class="modal" style="display: none;">
        <div class="modal-content small">
            <div class="modal-header">
                <h3 id="url-modal-title">Add URL Link</h3>
                <button class="close-btn" id="close-url-link">×</button>
            </div>
            <div class="modal-body">
                <div class="url-input-section">
                    <label for="url-input" class="url-input-label">Enter URL to link this item to:</label>
                    <input type="url" id="url-input" class="url-input-field" placeholder="https://example.com" autocomplete="url">
                    <div class="url-input-hint">This will convert the item from global to website-specific</div>
                </div>
                <div class="url-actions">
                    <button id="add-url-btn" class="add-btn">Add Link</button>
                    <button id="cancel-url-btn" class="cancel-btn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Note Modal -->
    <div id="edit-note-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Edit Note</h3>
                <button class="close-btn" id="close-edit-note">×</button>
            </div>
            <div class="modal-body">
                <input type="text" id="edit-note-title" class="input-field" placeholder="Note title">
                <textarea id="edit-note-content" class="textarea-field" placeholder="Note content" rows="6"></textarea>
                <div id="edit-note-url-section" class="url-edit-section" style="display: none;">
                    <label for="edit-note-url" class="url-edit-label">Add URL to convert to website-specific:</label>
                    <input type="url" id="edit-note-url" class="input-field" placeholder="https://example.com">
                </div>
                <div class="note-actions">
                    <button id="save-note-btn" class="add-btn">Save Changes</button>
                    <button id="cancel-edit-note-btn" class="cancel-btn">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Settings</h3>
                <button class="close-btn" id="close-settings">×</button>
            </div>
            <div class="modal-body">
                <div class="settings-content">
                    <div class="setting-group">
                        <label for="fullscreen-layout">Fullscreen Layout</label>
                        <select id="fullscreen-layout" class="setting-select">
                            <option value="horizontal">Horizontal Split</option>
                            <option value="vertical">Vertical Split</option>
                        </select>
                        <p class="setting-description">Choose how todos and notes are arranged in fullscreen mode.</p>
                    </div>
                    <div class="setting-group">
                        <label for="date-format">Date Format</label>
                        <select id="date-format" class="setting-select">
                            <option value="system">System Default</option>
                            <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                            <option value="dd-mm-yyyy">DD-MM-YYYY</option>
                        </select>
                        <p class="setting-description">Choose how dates are displayed throughout the extension.</p>
                    </div>
                    <div class="setting-group">
                        <label for="end-of-week">End of Week</label>
                        <select id="end-of-week" class="setting-select">
                            <option value="friday">Friday</option>
                            <option value="sunday">Sunday</option>
                        </select>
                        <p class="setting-description">Choose which day represents the end of the week for quick date selection.</p>
                    </div>
                    <div class="setting-group">
                        <label for="next-week-day">Next Week Day</label>
                        <select id="next-week-day" class="setting-select">
                            <option value="monday">Monday</option>
                            <option value="tuesday">Tuesday</option>
                            <option value="wednesday">Wednesday</option>
                            <option value="thursday">Thursday</option>
                            <option value="friday">Friday</option>
                            <option value="saturday">Saturday</option>
                            <option value="sunday">Sunday</option>
                        </select>
                        <p class="setting-description">Choose which day of the week "Next Week" should default to.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Undo Toast -->
    <div id="undo-toast" class="toast" style="display: none;">
        <div class="toast-content">
            <div class="toast-header">
                <span id="undo-title">ℹ Item Action</span>
                <button id="undo-btn" class="undo-btn">Undo</button>
            </div>
            <div id="undo-item-text" class="toast-item-text"></div>
            <div id="toast-progress-bar" class="toast-progress-bar"></div>
        </div>
    </div>

    <script src="popup.js"></script>
    <script>
        // Initialize fullscreen mode
        document.addEventListener('DOMContentLoaded', () => {
            // Create app instance for fullscreen mode
            const app = new TodoNotesApp();
            window.app = app;
            app.initFullscreenMode();
        });
    </script>
</body>
</html>
