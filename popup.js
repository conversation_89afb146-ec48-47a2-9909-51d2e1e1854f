// Popup script for Todo Notes Extension

class TodoNotesApp {
    constructor() {
        this.currentTab = null;
        this.currentScope = 'all'; // Default to all view
        this.currentGrouping = 'none';
        this.globalMode = false; // Keep for backward compatibility, but will be phased out
        this.currentEditingTodoId = null;

        // Per-item global mode states
        this.todoGlobalMode = false;
        this.noteGlobalMode = false;
        this.todos = [];
        this.notes = [];
        this.urlCache = {};
        this.settings = {
            defaultMode: 'sidebar', // Default to sidebar mode
            showCompleted: true,
            fullscreenLayout: 'vertical',
            closePreviousMode: true,
            dateFormat: 'system', // system, yyyy-mm-dd, dd-mm-yyyy
            endOfWeek: 'friday', // friday, sunday
            nextWeekDay: 'monday' // monday, tuesday, etc.
        };

        // Pagination state
        this.todosDisplayLimit = 5;
        this.notesDisplayLimit = 5;
        this.todosShowingAll = false;
        this.notesShowingAll = false;

        // Completed/Archived page state
        this.completedScope = 'all';
        this.completedGrouping = 'none';

        this.init();
    }
    
    async init() {
        await this.loadCurrentTab();
        await this.loadData();
        this.setupEventListeners();
        await this.loadTheme();

        // Skip sidebar mode check in fullscreen
        if (!document.body.classList.contains('fullscreen')) {
            this.checkSidebarMode();
        } else {
            // In fullscreen mode, default to global mode
            this.globalMode = true;
            this.updateGlobalModeDisplay();
        }

        this.updateUI();

        // Ensure counts are updated after everything is loaded (skip in fullscreen)
        if (!document.body.classList.contains('fullscreen')) {
            setTimeout(() => {
                this.updateCounts();
            }, 100);
        }
    }
    
    async loadCurrentTab() {
        try {
            // Skip tab loading in fullscreen mode
            if (document.body.classList.contains('fullscreen')) {
                this.currentTab = null;
                return;
            }

            this.currentTab = await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });

            if (this.currentTab) {
                const currentUrlEl = document.getElementById('current-url');
                if (currentUrlEl) {
                    currentUrlEl.textContent = this.formatUrl(this.currentTab.url);
                }
            }
        } catch (error) {
            console.error('Error loading current tab:', error);
        }
    }
    
    async loadData() {
        try {
            const result = await new Promise((resolve) => {
                chrome.storage.local.get(['todos', 'notes', 'urlCache', 'settings'], resolve);
            });

            this.todos = result.todos || [];
            this.notes = result.notes || [];
            this.urlCache = result.urlCache || {};
            this.settings = { ...this.settings, ...(result.settings || {}) };
            console.log('Loaded settings:', this.settings, 'from storage:', result.settings);
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }
    
    setupEventListeners() {
        // Scope selection
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchScope(e.target.dataset.scope);
            });
        });

        // Grouping selection
        const groupByEl = document.getElementById('group-by');
        if (groupByEl) {
            groupByEl.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;

                // Reset pagination when grouping changes
                this.todosShowingAll = false;
                this.notesShowingAll = false;

                this.updateUI();
            });
        }

        // New item buttons
        const newTodoBtnEl = document.getElementById('new-todo-btn');
        if (newTodoBtnEl) {
            newTodoBtnEl.addEventListener('click', () => {
                this.showAddTodo();
            });
        }

        const newNoteBtnEl = document.getElementById('new-note-btn');
        if (newNoteBtnEl) {
            newNoteBtnEl.addEventListener('click', () => {
                this.showAddNote();
            });
        }

        // Add todo - auto-save and improved handling

        // Simple todo input handling
        const todoInputEl = document.getElementById('todo-input');
        if (todoInputEl) {
            // Handle Enter key - create todo immediately
            todoInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const text = todoInputEl.value.trim();
                    if (text) {
                        this.addTodo();
                    } else {
                        this.hideAddTodo();
                    }
                }
            });

            // Handle ESC key - delete if empty, otherwise close
            todoInputEl.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.handleTodoEscape();
                }
            });

            // Auto-save on blur (when clicking outside)
            todoInputEl.addEventListener('blur', (e) => {
                // Small delay to allow other click events to process first
                setTimeout(() => {
                    // Check if the focus moved to the global toggle or other form elements
                    const activeElement = document.activeElement;
                    const isFormElement = activeElement && (
                        activeElement.id === 'global-mode-icon-todo' ||
                        activeElement.id === 'todo-due-date' ||
                        activeElement.closest('#add-todo-section')
                    );

                    if (!isFormElement) {
                        const text = todoInputEl.value.trim();
                        if (text) {
                            this.addTodo();
                        } else {
                            this.hideAddTodo();
                        }
                    }
                }, 100);
            });

            // Auto-save on input change (after a delay) - save and delete automatically
            let autoSaveTimeout;
            todoInputEl.addEventListener('input', (e) => {
                clearTimeout(autoSaveTimeout);
                const text = e.target.value.trim();
                if (text) {
                    autoSaveTimeout = setTimeout(() => {
                        this.addTodo(); // This will save and hide the form
                    }, 2000); // Auto-save after 2 seconds of no typing
                }
            });
        }

        const cancelTodoBtnEl = document.getElementById('cancel-todo-btn');
        if (cancelTodoBtnEl) {
            cancelTodoBtnEl.addEventListener('click', () => {
                this.hideAddTodo();
            });
        }

        // Calendar button (only exists in popup mode)
        const calendarBtnEl = document.getElementById('calendar-btn');
        if (calendarBtnEl) {
            calendarBtnEl.addEventListener('click', () => {
                this.showDatePicker();
            });
        }

        // Add note
        const addNoteBtnEl = document.getElementById('add-note-btn');
        if (addNoteBtnEl) {
            addNoteBtnEl.addEventListener('click', () => {
                this.addNote();
            });
        }

        // Simple note input handling
        const noteTitleEl = document.getElementById('note-title');
        const noteContentEl = document.getElementById('note-content');
        if (noteTitleEl && noteContentEl) {
            // Handle Ctrl+Enter key in content area - create note immediately
            noteContentEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    const title = noteTitleEl.value.trim();
                    const content = noteContentEl.value.trim();
                    if (content) {
                        this.addNote();
                    } else {
                        this.hideAddNote();
                    }
                }
            });

            // Handle ESC key - delete if empty, otherwise close
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    this.handleNoteEscape();
                }
            };

            noteTitleEl.addEventListener('keydown', handleEscape);
            noteContentEl.addEventListener('keydown', handleEscape);

            // Auto-save on blur (when clicking outside)
            const handleBlur = (e) => {
                setTimeout(() => {
                    // Check if the focus moved to the global toggle or other form elements
                    const activeElement = document.activeElement;
                    const isFormElement = activeElement && (
                        activeElement.id === 'global-mode-icon-note' ||
                        activeElement.id === 'note-title' ||
                        activeElement.id === 'note-content' ||
                        activeElement.closest('#add-note-section')
                    );

                    if (!isFormElement) {
                        const title = noteTitleEl.value.trim();
                        const content = noteContentEl.value.trim();
                        if (content) {
                            this.addNote();
                        } else {
                            this.hideAddNote();
                        }
                    }
                }, 100);
            };

            noteTitleEl.addEventListener('blur', handleBlur);
            noteContentEl.addEventListener('blur', handleBlur);

            // Auto-save on input change (after a delay) - save and delete automatically
            let autoSaveTimeout;
            const handleInput = (e) => {
                clearTimeout(autoSaveTimeout);
                const content = noteContentEl.value.trim();
                if (content) {
                    autoSaveTimeout = setTimeout(() => {
                        this.addNote(); // This will save and hide the form
                    }, 3000); // Auto-save after 3 seconds for notes (longer than todos)
                }
            };

            noteTitleEl.addEventListener('input', handleInput);
            noteContentEl.addEventListener('input', handleInput);
        }

        const cancelNoteBtnEl = document.getElementById('cancel-note-btn');
        if (cancelNoteBtnEl) {
            cancelNoteBtnEl.addEventListener('click', () => {
                this.hideAddNote();
            });
        }

        // Settings
        const settingsBtnEl = document.getElementById('settings-btn');
        if (settingsBtnEl) {
            settingsBtnEl.addEventListener('click', () => {
                this.openSettings();
            });
        }

        const closeSettingsEl = document.getElementById('close-settings');
        if (closeSettingsEl) {
            closeSettingsEl.addEventListener('click', () => {
                this.closeSettings();
            });
        }

        // Theme selector
        const themeSelectEl = document.getElementById('theme-select');
        if (themeSelectEl) {
            themeSelectEl.addEventListener('change', (e) => {
                this.setTheme(e.target.value);
            });
        }

        // Toggle completed todos
        const toggleCompletedEl = document.getElementById('toggle-completed');
        if (toggleCompletedEl) {
            toggleCompletedEl.addEventListener('click', (e) => {
                this.settings.showCompleted = !this.settings.showCompleted;
                e.target.textContent = this.settings.showCompleted ? 'Hide Completed' : 'Show Completed';
                this.updateUI();
                this.saveSettings();
            });
        }

        // Sidebar mode
        const sidebarBtnEl = document.getElementById('sidebar-btn');
        if (sidebarBtnEl) {
            sidebarBtnEl.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // Settings form
        const defaultModeEl = document.getElementById('default-mode');
        if (defaultModeEl) {
            defaultModeEl.addEventListener('change', (e) => {
                this.settings.defaultMode = e.target.value;
                this.saveSettings();
                console.log('Default mode changed to:', e.target.value);

                // Show feedback to user
                this.showSettingsSavedFeedback('Default mode updated to ' + e.target.value);
            });
        }

        const fullscreenLayoutEl = document.getElementById('fullscreen-layout');
        if (fullscreenLayoutEl) {
            fullscreenLayoutEl.addEventListener('change', (e) => {
                this.settings.fullscreenLayout = e.target.value;
                this.saveSettings();
            });
        }

        const closePreviousModeEl = document.getElementById('close-previous-mode');
        if (closePreviousModeEl) {
            closePreviousModeEl.addEventListener('change', (e) => {
                this.settings.closePreviousMode = e.target.checked;
                this.saveSettings();
            });
        }

        // Date format setting
        const dateFormatEl = document.getElementById('date-format');
        if (dateFormatEl) {
            dateFormatEl.addEventListener('change', (e) => {
                this.settings.dateFormat = e.target.value;
                this.saveSettings();
                this.updateUI(); // Refresh UI to show new date format
            });
        }

        // End of week setting
        const endOfWeekEl = document.getElementById('end-of-week');
        if (endOfWeekEl) {
            endOfWeekEl.addEventListener('change', (e) => {
                this.settings.endOfWeek = e.target.value;
                this.saveSettings();
            });
        }

        // Next week day setting
        const nextWeekDayEl = document.getElementById('next-week-day');
        if (nextWeekDayEl) {
            nextWeekDayEl.addEventListener('change', (e) => {
                this.settings.nextWeekDay = e.target.value;
                this.saveSettings();
            });
        }

        // Test mode button
        const testModeBtnEl = document.getElementById('test-mode-btn');
        if (testModeBtnEl) {
            testModeBtnEl.addEventListener('click', () => {
                this.testCurrentMode();
            });
        }

        // Date picker modal
        const closeDatePickerEl = document.getElementById('close-date-picker');
        if (closeDatePickerEl) {
            closeDatePickerEl.addEventListener('click', () => {
                const modalEl = document.getElementById('date-picker-modal');
                if (modalEl) {
                    modalEl.style.display = 'none';
                }
            });
        }

        // Quick date options
        document.querySelectorAll('.quick-date-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const days = e.target.dataset.days;
                const endOfWeek = e.target.dataset.endOfWeek;
                const nextWeek = e.target.dataset.nextWeek;
                let targetDate;

                if (days) {
                    targetDate = new Date();
                    targetDate.setDate(targetDate.getDate() + parseInt(days));
                } else if (endOfWeek) {
                    targetDate = new Date();
                    const dayOfWeek = targetDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
                    let targetDay = this.settings.endOfWeek === 'sunday' ? 0 : 5; // Sunday or Friday
                    let daysUntilTarget = (targetDay - dayOfWeek + 7) % 7;
                    if (daysUntilTarget === 0) daysUntilTarget = 7; // If today is the target day, go to next week
                    targetDate.setDate(targetDate.getDate() + daysUntilTarget);
                } else if (nextWeek) {
                    targetDate = new Date();
                    const dayOfWeek = targetDate.getDay();
                    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                    const targetDay = dayNames.indexOf(this.settings.nextWeekDay);
                    let daysUntilTarget = (targetDay - dayOfWeek + 7) % 7;
                    if (daysUntilTarget <= 0) daysUntilTarget += 7; // Always go to next week
                    targetDate.setDate(targetDate.getDate() + daysUntilTarget);
                }

                if (targetDate) {
                    const dateString = targetDate.toISOString().split('T')[0];
                    const datePickerInput = document.getElementById('date-picker-input');
                    if (datePickerInput) {
                        datePickerInput.value = dateString;
                    }
                }
            });
        });

        const setDateBtnEl = document.getElementById('set-date-btn');
        if (setDateBtnEl) {
            setDateBtnEl.addEventListener('click', () => {
                this.setSelectedDate();
            });
        }

        const clearDateBtnEl = document.getElementById('clear-date-btn');
        if (clearDateBtnEl) {
            clearDateBtnEl.addEventListener('click', () => {
                this.clearSelectedDate();
            });
        }

        // Date picker input - allow Enter key to save
        const datePickerInputEl = document.getElementById('date-picker-input');
        if (datePickerInputEl) {
            datePickerInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.setSelectedDate();
                }
            });
        }

        // Collapsible sections
        document.querySelectorAll('.section-header.collapsible').forEach(header => {
            header.addEventListener('click', (e) => {
                // Don't toggle if clicking on collapse button or new item button
                if (e.target.classList.contains('collapse-btn') ||
                    e.target.classList.contains('new-item-btn')) return;
                this.toggleSection(header.dataset.section);
            });
        });

        document.querySelectorAll('.collapse-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const sectionHeader = e.target.closest('.section-header') || e.target.closest('.completed-header');
                if (sectionHeader && sectionHeader.dataset.section) {
                    this.toggleSection(sectionHeader.dataset.section);
                }
            });
        });

        // Undo toast
        const undoBtnEl = document.getElementById('undo-btn');
        if (undoBtnEl) {
            undoBtnEl.addEventListener('click', () => {
                this.undoDelete();
            });
        }

        const closeToastEl = document.getElementById('close-toast');
        if (closeToastEl) {
            closeToastEl.addEventListener('click', () => {
                this.hideUndoToast();
            });
        }

        // Edit note modal
        const closeEditNoteEl = document.getElementById('close-edit-note');
        if (closeEditNoteEl) {
            closeEditNoteEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        const saveNoteBtnEl = document.getElementById('save-note-btn');
        if (saveNoteBtnEl) {
            saveNoteBtnEl.addEventListener('click', () => {
                this.saveEditedNote();
            });
        }

        const cancelEditNoteBtnEl = document.getElementById('cancel-edit-note-btn');
        if (cancelEditNoteBtnEl) {
            cancelEditNoteBtnEl.addEventListener('click', () => {
                this.closeEditNoteModal();
            });
        }

        // Completed/Archived page
        const viewCompletedBtnEl = document.getElementById('view-completed-btn');
        if (viewCompletedBtnEl) {
            viewCompletedBtnEl.addEventListener('click', () => {
                this.showCompletedArchivedPage();
            });
        }

        const backToMainBtnEl = document.getElementById('back-to-main-btn');
        if (backToMainBtnEl) {
            backToMainBtnEl.addEventListener('click', () => {
                this.hideCompletedArchivedPage();
            });
        }

        // Completed/Archived page scope and grouping
        const completedGroupByEl = document.getElementById('completed-group-by');
        if (completedGroupByEl) {
            completedGroupByEl.addEventListener('change', (e) => {
                this.completedGrouping = e.target.value;
                this.updateCompletedArchivedPage();
            });
        }

        // Setup completed page scope buttons (separate from main page)
        this.setupCompletedPageScopeButtons();

        // Fullscreen mode
        const fullscreenBtnEl = document.getElementById('fullscreen-btn');
        if (fullscreenBtnEl) {
            fullscreenBtnEl.addEventListener('click', () => {
                this.openFullscreen();
            });
        }

        // Floating window mode
        const floatingWindowBtnEl = document.getElementById('floating-window-btn');
        if (floatingWindowBtnEl) {
            floatingWindowBtnEl.addEventListener('click', () => {
                this.openFloatingWindow();
            });
        }

        // Popup window mode
        const popupWindowBtnEl = document.getElementById('popup-window-btn');
        if (popupWindowBtnEl) {
            popupWindowBtnEl.addEventListener('click', () => {
                this.openPopupWindow();
            });
        }

        // Close window button (for floating mode)
        const closeWindowBtnEl = document.getElementById('close-window-btn');
        if (closeWindowBtnEl) {
            closeWindowBtnEl.addEventListener('click', () => {
                window.close();
            });
        }

        // Global mode icon toggles (inline in forms)
        const globalModeIconTodoEl = document.getElementById('global-mode-icon-todo');
        const globalModeIconNoteEl = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodoEl) {
            globalModeIconTodoEl.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.todoGlobalMode = !this.todoGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();

                // Return focus to the input to prevent form from closing
                const todoInputEl = document.getElementById('todo-input');
                if (todoInputEl) {
                    setTimeout(() => {
                        todoInputEl.focus();
                    }, 10);
                }
            });
        }

        if (globalModeIconNoteEl) {
            globalModeIconNoteEl.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.noteGlobalMode = !this.noteGlobalMode;
                this.updateGlobalModeIcons();
                this.updatePlaceholders();

                // Return focus to the content input to prevent form from closing
                const noteContentEl = document.getElementById('note-content');
                if (noteContentEl) {
                    setTimeout(() => {
                        noteContentEl.focus();
                    }, 10);
                }
            });
        }

        // URL Link Modal
        const closeUrlLinkEl = document.getElementById('close-url-link');
        if (closeUrlLinkEl) {
            closeUrlLinkEl.addEventListener('click', () => {
                this.closeUrlLinkModal();
            });
        }

        const addUrlBtnEl = document.getElementById('add-url-btn');
        if (addUrlBtnEl) {
            addUrlBtnEl.addEventListener('click', () => {
                this.processUrlLink();
            });
        }

        const cancelUrlBtnEl = document.getElementById('cancel-url-btn');
        if (cancelUrlBtnEl) {
            cancelUrlBtnEl.addEventListener('click', () => {
                this.closeUrlLinkModal();
            });
        }

        const urlInputEl = document.getElementById('url-input');
        if (urlInputEl) {
            urlInputEl.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.processUrlLink();
                }
            });

            urlInputEl.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.closeUrlLinkModal();
                }
            });

            // Clear error styling on input
            urlInputEl.addEventListener('input', () => {
                urlInputEl.style.borderColor = '';
            });
        }

        // Setup drag and drop
        this.setupDragAndDrop();
    }

    setupDragAndDrop() {
        // Add event listeners to the container for drag and drop
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        [todosContainer, notesContainer].forEach(container => {
            if (container) {
                container.addEventListener('dragstart', this.handleDragStart.bind(this));
                container.addEventListener('dragover', this.handleDragOver.bind(this));
                container.addEventListener('drop', this.handleDrop.bind(this));
                container.addEventListener('dragend', this.handleDragEnd.bind(this));
            }
        });
    }

    handleDragStart(e) {
        if (e.target.classList.contains('item')) {
            this.draggedElement = e.target;
            e.target.classList.add('dragging');
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/html', e.target.outerHTML);

            // Add drag-active class to containers
            const containers = document.querySelectorAll('.items-list');
            containers.forEach(container => {
                container.classList.add('drag-active');
            });
        }
    }

    handleDragOver(e) {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';

        if (!this.draggedElement) return;

        // Remove any existing drop indicator
        this.removeDropIndicator();

        // Find the closest drop position
        const afterElement = this.getDragAfterElement(e.currentTarget, e.clientY);

        // Create and show drop indicator at the correct position
        this.createAndShowDropIndicator(e.currentTarget, afterElement);

        // Move the dragged element for preview (but don't commit yet)
        if (afterElement == null) {
            e.currentTarget.appendChild(this.draggedElement);
        } else {
            e.currentTarget.insertBefore(this.draggedElement, afterElement);
        }
    }

    handleDrop(e) {
        e.preventDefault();
        if (this.draggedElement) {
            this.reorderItems();
        }
        this.cleanupDragState();
    }

    handleDragEnd(e) {
        this.cleanupDragState();
    }

    cleanupDragState() {
        // Remove dragging class
        if (this.draggedElement) {
            this.draggedElement.classList.remove('dragging');
        }

        // Remove drag-active class from containers
        const containers = document.querySelectorAll('.items-list');
        containers.forEach(container => {
            container.classList.remove('drag-active');
        });

        // Remove drop indicator
        this.removeDropIndicator();

        this.draggedElement = null;
    }

    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.item:not(.dragging)')];

        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;

            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child };
            } else {
                return closest;
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    createAndShowDropIndicator(container, afterElement) {
        // Create the drop indicator
        const indicator = document.createElement('div');
        indicator.className = 'drop-indicator active';
        indicator.style.pointerEvents = 'none';

        // Insert the indicator at the correct position
        if (afterElement == null) {
            // Insert at the end
            container.appendChild(indicator);
        } else {
            // Insert before the afterElement
            container.insertBefore(indicator, afterElement);
        }

        // Store reference for easy removal
        this.currentDropIndicator = indicator;
    }

    removeDropIndicator() {
        if (this.currentDropIndicator) {
            this.currentDropIndicator.remove();
            this.currentDropIndicator = null;
        }
    }

    reorderItems() {
        // Get the new order from the DOM
        const todosContainer = document.getElementById('todos-list');
        const notesContainer = document.getElementById('notes-list');

        if (todosContainer) {
            const todoElements = [...todosContainer.querySelectorAll('.item[data-todo-id]')];
            const newTodoOrder = todoElements.map(el => el.dataset.todoId);
            this.reorderTodos(newTodoOrder);
        }

        if (notesContainer) {
            const noteElements = [...notesContainer.querySelectorAll('.item[data-note-id]')];
            const newNoteOrder = noteElements.map(el => el.dataset.noteId);
            this.reorderNotes(newNoteOrder);
        }
    }

    reorderTodos(newOrder) {
        const reorderedTodos = [];
        newOrder.forEach(id => {
            const todo = this.todos.find(t => t.id === id);
            if (todo) reorderedTodos.push(todo);
        });

        // Add any todos not in the new order (shouldn't happen, but safety check)
        this.todos.forEach(todo => {
            if (!newOrder.includes(todo.id)) {
                reorderedTodos.push(todo);
            }
        });

        this.todos = reorderedTodos;
        this.saveData();
    }

    reorderNotes(newOrder) {
        const reorderedNotes = [];
        newOrder.forEach(id => {
            const note = this.notes.find(n => n.id === id);
            if (note) reorderedNotes.push(note);
        });

        // Add any notes not in the new order (shouldn't happen, but safety check)
        this.notes.forEach(note => {
            if (!newOrder.includes(note.id)) {
                reorderedNotes.push(note);
            }
        });

        this.notes = reorderedNotes;
        this.saveData();
    }

    switchScope(scopeName) {
        // Update scope buttons
        document.querySelectorAll('.scope-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const scopeBtn = document.querySelector(`[data-scope="${scopeName}"]`);
        if (scopeBtn) {
            scopeBtn.classList.add('active');
        }

        this.currentScope = scopeName;

        // Show/hide grouping selector and update options based on scope
        const groupingSelector = document.getElementById('grouping-selector');
        const groupBySelect = document.getElementById('group-by');

        if (scopeName === 'all' || scopeName === 'global' || scopeName === 'website' || scopeName === 'current-tab') {
            groupingSelector.style.display = 'flex';

            // Update grouping options based on scope
            if (scopeName === 'website') {
                // Hide website option for website scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'current-tab') {
                // Hide website option for current-tab scope (since it's just one page)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else if (scopeName === 'global') {
                // Hide website option for global scope (since global items aren't tied to websites)
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'none';
                }
                // If currently grouped by website, switch to none
                if (this.currentGrouping === 'website') {
                    this.currentGrouping = 'none';
                    groupBySelect.value = 'none';
                }
            } else {
                // Show website option for all scope
                const websiteOption = groupBySelect.querySelector('option[value="website"]');
                if (websiteOption) {
                    websiteOption.style.display = 'block';
                }
            }

            // Handle Type option - only show for 'all' scope
            const typeOption = groupBySelect.querySelector('option[value="type"]');
            if (typeOption) {
                if (scopeName === 'all') {
                    typeOption.style.display = 'block';
                } else {
                    typeOption.style.display = 'none';
                    // If currently grouped by type, switch to none
                    if (this.currentGrouping === 'type') {
                        this.currentGrouping = 'none';
                        groupBySelect.value = 'none';
                    }
                }
            }
        } else {
            groupingSelector.style.display = 'none';
        }

        // Reset pagination when scope changes
        this.todosShowingAll = false;
        this.notesShowingAll = false;

        this.updateUI();
    }

    openSettings() {
        const modalEl = document.getElementById('settings-modal');
        const defaultModeEl = document.getElementById('default-mode');
        const fullscreenLayoutEl = document.getElementById('fullscreen-layout');
        const closePreviousModeEl = document.getElementById('close-previous-mode');
        const dateFormatEl = document.getElementById('date-format');
        const endOfWeekEl = document.getElementById('end-of-week');
        const nextWeekDayEl = document.getElementById('next-week-day');

        if (modalEl) modalEl.style.display = 'flex';
        if (defaultModeEl) {
            defaultModeEl.value = this.settings.defaultMode;
            console.log('Settings modal opened, defaultMode set to:', this.settings.defaultMode);
        }
        if (fullscreenLayoutEl) fullscreenLayoutEl.value = this.settings.fullscreenLayout;
        if (closePreviousModeEl) closePreviousModeEl.checked = this.settings.closePreviousMode;
        if (dateFormatEl) dateFormatEl.value = this.settings.dateFormat;
        if (endOfWeekEl) endOfWeekEl.value = this.settings.endOfWeek;
        if (nextWeekDayEl) nextWeekDayEl.value = this.settings.nextWeekDay;
    }

    showSettingsSavedFeedback(message) {
        // Create or update feedback element
        let feedbackEl = document.getElementById('settings-feedback');
        if (!feedbackEl) {
            feedbackEl = document.createElement('div');
            feedbackEl.id = 'settings-feedback';
            feedbackEl.className = 'settings-feedback';
            const modalBody = document.querySelector('#settings-modal .modal-body');
            if (modalBody) {
                modalBody.appendChild(feedbackEl);
            }
        }

        feedbackEl.textContent = message;
        feedbackEl.style.display = 'block';

        // Hide after 3 seconds
        setTimeout(() => {
            feedbackEl.style.display = 'none';
        }, 3000);
    }

    closeSettings() {
        document.getElementById('settings-modal').style.display = 'none';
    }

    testCurrentMode() {
        const mode = this.settings.defaultMode;
        console.log('Testing mode:', mode);

        switch (mode) {
            case 'floating':
                this.openFloatingWindow();
                this.showSettingsSavedFeedback('Opening floating window...');
                break;
            case 'popup':
                this.showSettingsSavedFeedback('Popup mode is the current window');
                break;
            case 'sidebar':
            default:
                this.openSidebar();
                this.showSettingsSavedFeedback('Opening sidebar...');
                break;
        }
    }

    toggleSidebar() {
        const isSidebarMode = document.body.classList.contains('sidebar-mode');

        if (isSidebarMode) {
            // Close sidebar
            this.closeSidebar();
        } else {
            // Open sidebar
            this.openSidebar();
        }
    }

    async openSidebar() {
        try {
            // If we're in floating mode or popup mode and user wants to close previous mode
            if (this.settings.closePreviousMode) {
                if (document.body.classList.contains('floating-mode')) {
                    // Close floating window
                    window.close();
                    return; // Don't continue since window is closing
                } else if (!document.body.classList.contains('sidebar-mode')) {
                    // We're in popup mode, close it by sending message to background
                    chrome.runtime.sendMessage({ action: 'closePreviousMode', newMode: 'sidebar' });
                }
            }

            // Get the current window first
            const currentWindow = await chrome.windows.getCurrent();
            // Use Chrome's Side Panel API with the actual window ID
            await chrome.sidePanel.open({ windowId: currentWindow.id });
        } catch (error) {
            console.error('Failed to open side panel:', error);
            // Fallback: just show a message to the user
            alert('Please use the Chrome side panel setting to enable sidebar mode. Go to chrome://extensions, find this extension, and click "Details" then enable "Allow in side panel".');
        }
    }

    closeSidebar() {
        // Close the current window if we're in sidebar mode
        if (document.body.classList.contains('sidebar-mode')) {
            window.close();
        }
    }



    checkSidebarMode() {
        // Check if we're in sidebar mode based on URL parameters or window characteristics
        const urlParams = new URLSearchParams(window.location.search);
        const isSidebar = urlParams.get('sidebar') === 'true';

        // Better detection for Chrome's native side panel
        // Side panels typically have specific characteristics:
        // 1. They're chrome-extension protocol
        // 2. They have a constrained width (usually around 320-400px)
        // 3. They don't have window controls (no close button in window frame)
        // 4. The window.chrome.sidePanel API is available
        const isNativeSidePanel = window.location.protocol === 'chrome-extension:' &&
                                  window.innerWidth <= 500 &&
                                  window.innerWidth >= 280 &&
                                  // Check if we're likely in a side panel context
                                  (typeof chrome !== 'undefined' && chrome.sidePanel);

        // Check if we're in floating window mode (explicit parameter or window characteristics)
        const isFloatingWindow = window.location.search.includes('floating=true') ||
                                // Floating windows are typically larger and have specific dimensions
                                (window.outerWidth >= 350 && window.outerWidth <= 500 &&
                                 window.outerHeight >= 500 && window.outerHeight <= 800 &&
                                 !isNativeSidePanel);

        console.log('Mode detection:', {
            isSidebar,
            isNativeSidePanel,
            isFloatingWindow,
            innerWidth: window.innerWidth,
            outerWidth: window.outerWidth,
            protocol: window.location.protocol,
            search: window.location.search,
            hasSidePanelAPI: typeof chrome !== 'undefined' && chrome.sidePanel
        });

        if (isSidebar || isNativeSidePanel) {
            // Add sidebar-specific styling
            document.body.classList.add('sidebar-mode');
            document.body.classList.remove('floating-mode');
            this.updateButtonVisibility('sidebar');

            // Set up auto-refresh for sidebar mode
            this.setupTabChangeRefresh();
            console.log('Sidebar mode activated');
        } else if (isFloatingWindow) {
            document.body.classList.add('floating-mode');
            document.body.classList.remove('sidebar-mode');
            this.updateButtonVisibility('floating');

            // Set up auto-refresh for floating mode too
            this.setupTabChangeRefresh();
            console.log('Floating mode activated');
        } else {
            // Normal popup mode
            document.body.classList.remove('sidebar-mode', 'floating-mode');
            this.updateButtonVisibility('popup');

            // Set up auto-refresh for popup mode too
            this.setupTabChangeRefresh();
            console.log('Popup mode activated');
        }
    }

    updateButtonVisibility(mode) {
        const sidebarBtn = document.getElementById('sidebar-btn');
        const floatingBtn = document.getElementById('floating-window-btn');
        const popupBtn = document.getElementById('popup-window-btn');
        const closeWindowBtn = document.getElementById('close-window-btn');

        if (mode === 'sidebar') {
            // In sidebar mode: show close button instead of sidebar button, show floating and popup buttons, hide close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '×';
                sidebarBtn.title = 'Close Sidebar';
            }
            if (floatingBtn) floatingBtn.style.display = 'inline-flex';
            if (popupBtn) popupBtn.style.display = 'inline-flex';
            if (closeWindowBtn) closeWindowBtn.style.display = 'none';
        } else if (mode === 'floating') {
            // In floating mode: show sidebar and popup buttons, hide floating button, show close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '▶️';
                sidebarBtn.title = 'Open in Sidebar';
            }
            if (floatingBtn) floatingBtn.style.display = 'none';
            if (popupBtn) popupBtn.style.display = 'inline-flex';
            if (closeWindowBtn) {
                closeWindowBtn.style.display = 'inline-flex';
                closeWindowBtn.innerHTML = '×';
                closeWindowBtn.title = 'Close Window';
            }
        } else {
            // Normal popup mode: show sidebar and floating buttons, hide popup button, hide close window button
            if (sidebarBtn) {
                sidebarBtn.innerHTML = '▶️';
                sidebarBtn.title = 'Open in Sidebar';
            }
            if (floatingBtn) floatingBtn.style.display = 'inline-flex';
            if (popupBtn) popupBtn.style.display = 'none';
            if (closeWindowBtn) closeWindowBtn.style.display = 'none';
        }
    }

    setupTabChangeRefresh() {
        // Listen for messages from background script about tab changes
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            if (message.action === 'tabChanged' || message.action === 'activeTabChanged') {
                // Refresh current tab info and update UI
                this.loadCurrentTab().then(() => {
                    this.updateUI();
                });
            }
        });

        // Listen for tab changes directly (backup method)
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async () => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Listen for tab changes in all modes (except fullscreen)
        if (!document.body.classList.contains('fullscreen')) {
            this.setupTabChangeListener();
        }
    }

    setupTabChangeListener() {
        // Use Chrome's tab API to listen for tab changes
        if (chrome.tabs && chrome.tabs.onActivated) {
            chrome.tabs.onActivated.addListener(async (activeInfo) => {
                await this.loadCurrentTab();
                this.updateUI();
            });
        }

        // Also listen for tab updates (URL changes within same tab)
        if (chrome.tabs && chrome.tabs.onUpdated) {
            chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
                if (changeInfo.status === 'complete' && tab.active) {
                    await this.loadCurrentTab();
                    this.updateUI();
                }
            });
        }

        // Fallback polling for cases where events don't fire
        setInterval(async () => {
            const newTab = await this.getCurrentTabInfo();
            if (newTab && this.currentTab && newTab.url !== this.currentTab.url) {
                await this.loadCurrentTab();
                this.updateUI();
            }
        }, 1000); // Check every second as fallback
    }

    async getCurrentTabInfo() {
        try {
            return await new Promise((resolve) => {
                chrome.runtime.sendMessage({ action: 'getCurrentTab' }, resolve);
            });
        } catch (error) {
            console.error('Error getting current tab info:', error);
            return null;
        }
    }

    addTodo() {
        const input = document.getElementById('todo-input');
        const dueDateInput = document.getElementById('todo-due-date');
        const text = input.value.trim();

        if (!text) return;

        const todo = {
            id: this.generateId(),
            text: text,
            completed: false,
            dueDate: dueDateInput.value || null,
            createdAt: new Date().toISOString(),
            url: this.todoGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in todo global mode
            baseUrl: this.getScopeBaseUrl(),
            notes: '' // Add notes field for todo-note relationships
        };

        this.todos.unshift(todo); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddTodo();
    }




    
    addNote() {
        const titleInput = document.getElementById('note-title');
        const contentInput = document.getElementById('note-content');
        const title = titleInput.value.trim();
        const content = contentInput.value.trim();

        if (!content) return;

        const note = {
            id: this.generateId(),
            title: title || content.split('\n')[0].substring(0, 50) + (content.length > 50 ? '...' : ''),
            content: content,
            createdAt: new Date().toISOString(),
            url: this.noteGlobalMode ? 'global' : (this.currentTab?.url || 'global'), // Use global URL if in note global mode
            baseUrl: this.getScopeBaseUrl(),
            linkedTodoId: null // For linking notes to todos
        };

        this.notes.unshift(note); // Add to beginning of array
        this.saveData();
        this.updateUI();
        this.hideAddNote();
    }


    
    getScopeUrl() {
        // Always save full URL, but scope determines what we show
        return this.currentTab?.url || '';
    }

    getScopeBaseUrl() {
        return this.currentTab?.baseUrl || '';
    }

    getFilteredTodos() {
        const allTodos = this.todos;

        switch (this.currentScope) {
            case 'global':
                return allTodos.filter(todo => todo.url === 'global');
            case 'current-tab':
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allTodos.filter(todo => {
                    try {
                        if (todo.url === 'global') return false; // Exclude global todos from website view
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allTodos; // Include all todos including global ones
            default:
                return allTodos.filter(todo => todo.url === this.currentTab?.url);
        }
    }

    getFilteredNotes() {
        const allNotes = this.notes.filter(note => !note.archived);

        switch (this.currentScope) {
            case 'global':
                return allNotes.filter(note => note.url === 'global');
            case 'current-tab':
                return allNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return allNotes.filter(note => {
                    try {
                        if (note.url === 'global') return false; // Exclude global notes from website view
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
                return allNotes; // Include all notes including global ones
            default:
                return allNotes.filter(note => note.url === this.currentTab?.url);
        }
    }

    updateUI() {
        // Handle fullscreen mode separately
        if (document.body.classList.contains('fullscreen')) {
            this.updateFullscreenUI();
            return;
        }

        this.updateTodosList();
        this.updateNotesList();
        this.updateCounts();

        // Update placeholder text based on current scope and per-item global mode
        this.updatePlaceholders();

        this.updateGlobalModeDisplay();
    }

    updatePlaceholders() {
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        // Update todo placeholder based on per-item global mode
        if (todoInput) {
            if (this.todoGlobalMode) {
                todoInput.placeholder = 'Add a global todo (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        todoInput.placeholder = 'Add a new todo for this page...';
                        break;
                    case 'website':
                        todoInput.placeholder = 'Add a new todo for this website...';
                        break;
                    case 'all':
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                    default:
                        todoInput.placeholder = 'Add a new todo...';
                        break;
                }
            }
        }

        // Update note placeholder based on per-item global mode
        if (noteContent) {
            if (this.noteGlobalMode) {
                noteContent.placeholder = 'Add a global note (not linked to any URL)...';
            } else {
                switch (this.currentScope) {
                    case 'current-tab':
                        noteContent.placeholder = 'Add a new note for this page...';
                        break;
                    case 'website':
                        noteContent.placeholder = 'Add a new note for this website...';
                        break;
                    case 'all':
                        noteContent.placeholder = 'Add a new note...';
                        break;
                    default:
                        noteContent.placeholder = 'Add a new note...';
                        break;
                }
            }
        }
    }

    updateGlobalModeDisplay() {
        this.updateGlobalModeIcons();

        // Disable scope selector when in global mode
        const scopeSelector = document.getElementById('scope-selector');
        if (scopeSelector) {
            scopeSelector.disabled = this.globalMode;
            scopeSelector.style.opacity = this.globalMode ? '0.5' : '1';
        }
    }

    updateGlobalModeIcons() {
        const globalModeIconTodo = document.getElementById('global-mode-icon-todo');
        const globalModeIconNote = document.getElementById('global-mode-icon-note');

        if (globalModeIconTodo) {
            if (this.todoGlobalMode) {
                globalModeIconTodo.classList.add('active');
            } else {
                globalModeIconTodo.classList.remove('active');
            }
        }

        if (globalModeIconNote) {
            if (this.noteGlobalMode) {
                globalModeIconNote.classList.add('active');
            } else {
                globalModeIconNote.classList.remove('active');
            }
        }
    }

    groupItems(items, groupBy) {
        if (groupBy === 'none') {
            return { 'All Items': items };
        }

        const groups = {};

        items.forEach(item => {
            let groupKey;

            switch (groupBy) {
                case 'website':
                    try {
                        const url = new URL(item.url);
                        // Extract base URL (hostname + path without query/hash)
                        groupKey = url.hostname + (url.pathname !== '/' ? url.pathname : '');
                    } catch (error) {
                        groupKey = 'Global';
                    }
                    break;
                case 'due-date':
                    groupKey = this.getImprovedDueDateGroup(item.dueDate);
                    break;
                case 'created-date':
                    groupKey = this.getImprovedCreatedDateGroup(item.createdAt);
                    break;
                case 'type':
                    groupKey = item.url === 'global' ? 'Global' : 'Website-specific';
                    break;
                default:
                    groupKey = 'All Items';
            }

            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
        });

        // Sort groups for due-date: logical order
        if (groupBy === 'due-date') {
            const sortedGroups = {};
            const groupOrder = ['Today', 'Tomorrow', 'Soon', 'Later', 'No Due Date'];

            // Add groups in the specified order and sort items within each group
            groupOrder.forEach(groupName => {
                if (groups[groupName]) {
                    // Sort items within group by due date
                    groups[groupName].sort((a, b) => {
                        if (!a.dueDate && !b.dueDate) return 0;
                        if (!a.dueDate) return 1;
                        if (!b.dueDate) return -1;
                        return new Date(a.dueDate) - new Date(b.dueDate);
                    });
                    sortedGroups[groupName] = groups[groupName];
                }
            });

            return sortedGroups;
        }

        // Sort groups for created-date: logical order
        if (groupBy === 'created-date') {
            const sortedGroups = {};
            const groupOrder = ['Today', 'Yesterday', 'Recently', 'Ages ago 🦖', 'No Date'];

            // Add groups in the specified order and sort items within each group
            groupOrder.forEach(groupName => {
                if (groups[groupName]) {
                    // Sort items within group by created date (newest first)
                    groups[groupName].sort((a, b) => {
                        if (!a.createdAt && !b.createdAt) return 0;
                        if (!a.createdAt) return 1;
                        if (!b.createdAt) return -1;
                        return new Date(b.createdAt) - new Date(a.createdAt);
                    });
                    sortedGroups[groupName] = groups[groupName];
                }
            });

            return sortedGroups;
        }

        // Sort groups for type: website-specific first, then global
        if (groupBy === 'type') {
            const sortedGroups = {};
            const websiteSpecific = [];
            const global = [];

            Object.keys(groups).forEach(key => {
                if (key === 'Global') {
                    global.push(key);
                } else {
                    websiteSpecific.push(key);
                }
            });

            // Add website-specific first, then global
            [...websiteSpecific, ...global].forEach(key => {
                sortedGroups[key] = groups[key];
            });

            return sortedGroups;
        }

        return groups;
    }

    updateTodosList() {
        const todosList = document.getElementById('todos-list');
        const filteredTodos = this.getFilteredTodos().filter(todo => !todo.completed);

        if (filteredTodos.length === 0) {
            todosList.innerHTML = '';
            return;
        }

        todosList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                todosList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            // Render todos with pagination (newest first since array is already ordered with unshift)
            const todosToShow = this.todosShowingAll ? filteredTodos : filteredTodos.slice(0, this.todosDisplayLimit);

            todosToShow.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                todosList.appendChild(todoEl);
            });

            // Add "Show more" button if there are more todos to show
            if (!this.todosShowingAll && filteredTodos.length > this.todosDisplayLimit) {
                const showMoreBtn = document.createElement('button');
                showMoreBtn.className = 'show-more-btn';
                showMoreBtn.textContent = `Show more (${filteredTodos.length - this.todosDisplayLimit} remaining)`;
                showMoreBtn.addEventListener('click', () => {
                    this.todosShowingAll = true;
                    this.updateTodosList();
                });
                todosList.appendChild(showMoreBtn);
            }
        }
    }



    updateNotesList() {
        const notesList = document.getElementById('notes-list');
        const filteredNotes = this.getFilteredNotes();

        if (filteredNotes.length === 0) {
            notesList.innerHTML = '';
            return;
        }

        notesList.innerHTML = '';

        // Apply grouping if in 'all' mode and grouping is enabled
        if (this.currentScope === 'all' && this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header collapsible';
                groupHeader.dataset.section = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}`;
                groupHeader.innerHTML = `
                    <h4>${groupName} (${groups[groupName].length})</h4>
                    <button class="collapse-btn">▼</button>
                `;

                const groupContent = document.createElement('div');
                groupContent.className = 'section-content';
                groupContent.id = `notes-group-${groupName.replace(/\s+/g, '-').toLowerCase()}-content`;

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                notesList.appendChild(groupContainer);

                // Add click listener for collapsible functionality
                groupHeader.addEventListener('click', (e) => {
                    if (e.target.classList.contains('collapse-btn')) return;
                    this.toggleSection(groupHeader.dataset.section);
                });

                const collapseBtn = groupHeader.querySelector('.collapse-btn');
                if (collapseBtn) {
                    collapseBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleSection(groupHeader.dataset.section);
                    });
                }
            });
        } else {
            // Render notes with pagination
            const notesToShow = this.notesShowingAll ? filteredNotes : filteredNotes.slice(0, this.notesDisplayLimit);

            notesToShow.forEach(note => {
                const noteEl = this.createNoteElement(note);
                notesList.appendChild(noteEl);
            });

            // Add "Show more" button if there are more notes to show
            if (!this.notesShowingAll && filteredNotes.length > this.notesDisplayLimit) {
                const showMoreBtn = document.createElement('button');
                showMoreBtn.className = 'show-more-btn';
                showMoreBtn.textContent = `Show more (${filteredNotes.length - this.notesDisplayLimit} remaining)`;
                showMoreBtn.addEventListener('click', () => {
                    this.notesShowingAll = true;
                    this.updateNotesList();
                });
                notesList.appendChild(showMoreBtn);
            }
        }
    }

    createTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = `item ${todo.completed ? 'completed' : ''}`;
        todoEl.draggable = true;
        todoEl.dataset.todoId = todo.id;
        todoEl.dataset.itemType = 'todo';

        const dueDateDisplay = this.formatDueDate(todo.dueDate);
        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';

        const faviconUrl = todo.url !== 'global' ? this.getFaviconUrl(todo.url) : null;

        todoEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <input type="checkbox" class="item-checkbox" ${todo.completed ? 'checked' : ''} data-todo-id="${todo.id}">
            <div class="item-content">
                <div class="item-text" data-todo-id="${todo.id}" style="cursor: pointer;">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">
                        ${faviconUrl ? `<img src="${faviconUrl}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}${todo.url === 'global' ? '🌐 ' : ''}${todo.text}
                </div>
                <div class="item-meta">
                    ${dueDateDisplay ? `<span class="item-due-date ${this.getDueDateClass(todo.dueDate)}">${dueDateDisplay}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-btn" data-todo-id="${todo.id}" title="Edit">✏️</button>
                <button class="date-btn" data-todo-id="${todo.id}" title="Set Due Date">📅</button>
                ${todo.url === 'global' ? `<button class="link-btn" data-todo-id="${todo.id}" title="Add URL">🔗</button>` : `<button class="convert-to-global-btn" data-todo-id="${todo.id}" title="Convert to Global" style="opacity: 0.6;">🌐</button>`}
                <button class="delete-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const checkbox = todoEl.querySelector('.item-checkbox');
        checkbox.addEventListener('change', () => this.toggleTodo(todo.id));

        const textEl = todoEl.querySelector('.item-text');
        textEl.addEventListener('click', () => this.editTodo(todo.id));

        const editBtn = todoEl.querySelector('.edit-btn');
        editBtn.addEventListener('click', () => this.editTodo(todo.id));

        const dateBtn = todoEl.querySelector('.date-btn');
        dateBtn.addEventListener('click', () => this.setDueDate(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        // Add link button event listener for global todos
        const linkBtn = todoEl.querySelector('.link-btn');
        if (linkBtn) {
            linkBtn.addEventListener('click', () => this.addUrlToGlobalTodo(todo.id));
        }

        // Add convert to global button event listener for non-global todos
        const convertBtn = todoEl.querySelector('.convert-to-global-btn');
        if (convertBtn) {
            convertBtn.addEventListener('click', () => this.convertTodoToGlobal(todo.id));
        }

        return todoEl;
    }

    createNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item';
        noteEl.draggable = true;
        noteEl.dataset.noteId = note.id;
        noteEl.dataset.itemType = 'note';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';

        const createdDate = this.formatDate(note.createdAt);

        noteEl.innerHTML = `
            <div class="drag-handle" title="Drag to reorder">⋮⋮</div>
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">
                        ${this.getFaviconUrl(note.url) ? `<img src="${this.getFaviconUrl(note.url)}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}
                    ${note.title ? `<strong>${note.url === 'global' ? '🌐 ' : ''}${note.title}</strong> <span class="created-date" title="Created on ${createdDate}">Created ${createdDate}</span><br>` : ''}
                    ${note.url === 'global' && !note.title ? '🌐 ' : ''}${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                    ${!note.title ? `<div class="note-meta"><span class="created-date" title="Created on ${createdDate}">Created ${createdDate}</span></div>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="edit-note-btn" data-note-id="${note.id}" title="Edit">✏️</button>
                <button class="archive-note-btn" data-note-id="${note.id}" title="Archive">📁</button>
                ${note.url === 'global' ? `<button class="link-note-btn" data-note-id="${note.id}" title="Add URL">🔗</button>` : `<button class="convert-note-to-global-btn" data-note-id="${note.id}" title="Convert to Global" style="opacity: 0.6;">🌐</button>`}
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const editBtn = noteEl.querySelector('.edit-note-btn');
        editBtn.addEventListener('click', () => this.editNote(note.id));

        const archiveBtn = noteEl.querySelector('.archive-note-btn');
        archiveBtn.addEventListener('click', () => this.archiveNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        // Add link button event listener for global notes
        const linkBtn = noteEl.querySelector('.link-note-btn');
        if (linkBtn) {
            linkBtn.addEventListener('click', () => this.addUrlToGlobalNote(note.id));
        }

        // Add convert to global button event listener for non-global notes
        const convertBtn = noteEl.querySelector('.convert-note-to-global-btn');
        if (convertBtn) {
            convertBtn.addEventListener('click', () => this.convertNoteToGlobal(note.id));
        }

        return noteEl;
    }



    formatGroupHeader(groupKey, groupBy) {
        switch (groupBy) {
            case 'url':
                return `📍 ${groupKey}`;
            case 'due-date':
                return `📅 ${groupKey}`;
            case 'created-date':
                return `📅 ${groupKey}`;
            case 'type':
                return groupKey === 'Global' ? `🌐 ${groupKey}` : `🔗 ${groupKey}`;
            default:
                return groupKey;
        }
    }

    formatUrl(url) {
        if (url === 'global') return 'Global';
        try {
            const urlObj = new URL(url);
            return urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
        } catch (error) {
            return url;
        }
    }

    getFaviconUrl(url) {
        try {
            const urlObj = new URL(url);
            return `https://www.google.com/s2/favicons?domain=${urlObj.hostname}&sz=16`;
        } catch (error) {
            return null;
        }
    }

    formatDueDate(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else {
            return this.formatDate(dueDate);
        }
    }

    getDueDateClass(dueDate) {
        if (!dueDate) return '';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const due = new Date(dueDate);

        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'tomorrow';
        } else {
            return '';
        }
    }

    toggleTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo) return;

        // Find the todo element in the DOM
        const todoElement = document.querySelector(`[data-todo-id="${todoId}"]`)?.closest('.item');

        if (todo.completed) {
            // If unchecking, just update immediately
            todo.completed = false;
            delete todo.completedAt;
            this.saveData();
            this.updateUI();
        } else {
            // If checking, animate the completion
            todo.completed = true;
            todo.completedAt = new Date().toISOString();

            if (todoElement) {
                this.animateTodoCompletion(todoElement, () => {
                    this.saveData();
                    this.updateUI();
                });
            } else {
                this.saveData();
                this.updateUI();
            }
        }
    }

    animateTodoCompletion(todoElement, callback) {
        // Prevent multiple animations on the same element
        if (todoElement.classList.contains('completing')) return;

        todoElement.classList.add('completing');

        // Step 1: Checkbox animation (already handled by CSS)
        const checkbox = todoElement.querySelector('.item-checkbox');
        const itemText = todoElement.querySelector('.item-text');

        // Step 2: Strike through animation after 300ms
        setTimeout(() => {
            if (itemText) {
                itemText.style.transition = 'all 0.3s ease';
                itemText.style.textDecoration = 'line-through';
                itemText.style.color = '#9ca3af';
            }

            // Step 3: Fade out animation after another 700ms (total 1000ms)
            setTimeout(() => {
                todoElement.style.transition = 'opacity 1s ease, transform 1s ease';
                todoElement.style.opacity = '0';
                todoElement.style.transform = 'translateX(-20px)';

                // Step 4: Complete the process after fade out (total 2000ms)
                setTimeout(() => {
                    todoElement.classList.remove('completing');
                    callback();
                }, 1000);
            }, 700);
        }, 300);
    }

    editTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            // Find the todo element
            const todoEl = document.querySelector(`[data-todo-id="${todoId}"]`);
            if (!todoEl) return;

            const textEl = todoEl.querySelector('.item-text');
            if (!textEl) return;

            // Check if already editing
            if (textEl.querySelector('input') || textEl.querySelector('.edit-form')) return;

            // Store original text
            const originalText = textEl.innerHTML;

            // Check if this is a global item that can have URL added
            const isGlobal = todo.url === 'global';

            if (isGlobal) {
                // Create enhanced edit form for global items with URL option
                this.createGlobalTodoEditForm(todo, textEl, originalText);
            } else {
                // Create simple text input for non-global items
                this.createSimpleTodoEdit(todo, textEl, originalText);
            }
        }
    }

    createSimpleTodoEdit(todo, textEl, originalText) {
        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = todo.text;
        input.className = 'inline-edit-input';

        // Replace text with input
        textEl.innerHTML = '';
        textEl.appendChild(input);
        input.focus();
        input.select();

        // Handle save
        const saveEdit = () => {
            const newText = input.value.trim();
            if (newText && newText !== todo.text) {
                todo.text = newText;
                this.saveData();
                this.updateUI();
            } else {
                // Restore original text if no changes or empty
                textEl.innerHTML = originalText;
            }
        };

        // Handle cancel
        const cancelEdit = () => {
            textEl.innerHTML = originalText;
        };

        // Event listeners
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        input.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        input.addEventListener('blur', () => {
            saveEdit();
        });
    }

    createGlobalTodoEditForm(todo, textEl, originalText) {
        // Create edit form container
        const editForm = document.createElement('div');
        editForm.className = 'edit-form global-edit-form';

        // Create text input
        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.value = todo.text;
        textInput.className = 'edit-text-input';
        textInput.placeholder = 'Todo text...';

        // Create URL input
        const urlInput = document.createElement('input');
        urlInput.type = 'url';
        urlInput.value = '';
        urlInput.className = 'edit-url-input';
        urlInput.placeholder = 'Add URL to convert to website-specific (optional)...';

        // Create action buttons
        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'edit-buttons';

        const saveBtn = document.createElement('button');
        saveBtn.className = 'edit-save-btn';
        saveBtn.innerHTML = '✓';
        saveBtn.title = 'Save changes';

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'edit-cancel-btn';
        cancelBtn.innerHTML = '✖';
        cancelBtn.title = 'Cancel editing';

        buttonContainer.appendChild(saveBtn);
        buttonContainer.appendChild(cancelBtn);

        // Assemble form
        editForm.appendChild(textInput);
        editForm.appendChild(urlInput);
        editForm.appendChild(buttonContainer);

        // Replace text with form
        textEl.innerHTML = '';
        textEl.appendChild(editForm);
        textInput.focus();
        textInput.select();

        // Handle save
        const saveEdit = () => {
            const newText = textInput.value.trim();
            const newUrl = urlInput.value.trim();

            if (!newText) {
                // Restore original if text is empty
                textEl.innerHTML = originalText;
                return;
            }

            let hasChanges = false;

            // Update text if changed
            if (newText !== todo.text) {
                todo.text = newText;
                hasChanges = true;
            }

            // Update URL if provided (convert from global to website-specific)
            if (newUrl) {
                try {
                    // Validate URL
                    new URL(newUrl);
                    todo.url = newUrl;
                    hasChanges = true;
                } catch (e) {
                    // Invalid URL, show error and don't save
                    urlInput.style.borderColor = '#ef4444';
                    urlInput.focus();
                    return;
                }
            }

            if (hasChanges) {
                this.saveData();
                this.updateUI();
            } else {
                // Restore original text if no changes
                textEl.innerHTML = originalText;
            }
        };

        // Handle cancel
        const cancelEdit = () => {
            textEl.innerHTML = originalText;
        };

        // Event listeners
        saveBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            saveEdit();
        });

        cancelBtn.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            cancelEdit();
        });

        textInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            }
        });

        textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        urlInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });

        // Clear URL input error styling on input
        urlInput.addEventListener('input', () => {
            urlInput.style.borderColor = '';
        });
    }

    setDueDate(todoId) {
        this.currentEditingTodoId = todoId;
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const currentDate = todo.dueDate || '';
            document.getElementById('date-picker-input').value = currentDate;
            document.getElementById('date-picker-modal').style.display = 'flex';
        }
    }

    deleteTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            this.todos = this.todos.filter(t => t.id !== todoId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Todo deleted', todoBackup.text, () => {
                this.todos.push(todoBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    editNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            this.currentEditingNoteId = noteId;

            const titleEl = document.getElementById('edit-note-title');
            const contentEl = document.getElementById('edit-note-content');
            const urlEl = document.getElementById('edit-note-url');
            const urlSectionEl = document.getElementById('edit-note-url-section');
            const modalEl = document.getElementById('edit-note-modal');

            if (titleEl) titleEl.value = note.title;
            if (contentEl) contentEl.value = note.content;

            // Show URL section only for global notes
            if (note.url === 'global') {
                if (urlSectionEl) urlSectionEl.style.display = 'block';
                if (urlEl) {
                    urlEl.value = '';
                    urlEl.style.borderColor = '';
                }
            } else {
                if (urlSectionEl) urlSectionEl.style.display = 'none';
            }

            if (modalEl) modalEl.style.display = 'flex';
        }
    }

    closeEditNoteModal() {
        const modalEl = document.getElementById('edit-note-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
        this.currentEditingNoteId = null;
    }

    showUrlLinkModal(title, label) {
        const modalEl = document.getElementById('url-link-modal');
        const titleEl = document.getElementById('url-modal-title');
        const labelEl = document.querySelector('.url-input-label');
        const inputEl = document.getElementById('url-input');

        if (titleEl) titleEl.textContent = title;
        if (labelEl) labelEl.textContent = label;
        if (inputEl) {
            inputEl.value = 'https://';
            inputEl.focus();
            // Select the text after 'https://' for easy typing
            setTimeout(() => {
                inputEl.setSelectionRange(8, inputEl.value.length);
            }, 10);
        }
        if (modalEl) modalEl.style.display = 'flex';
    }

    closeUrlLinkModal() {
        const modalEl = document.getElementById('url-link-modal');
        const inputEl = document.getElementById('url-input');

        if (modalEl) modalEl.style.display = 'none';
        if (inputEl) {
            inputEl.value = '';
            inputEl.style.borderColor = '';
        }

        this.currentUrlLinkItemId = null;
        this.currentUrlLinkItemType = null;
    }

    processUrlLink() {
        const inputEl = document.getElementById('url-input');
        const url = inputEl ? inputEl.value.trim() : '';

        if (!url || url === 'https://') {
            this.closeUrlLinkModal();
            return;
        }

        // Validate URL
        try {
            new URL(url);
        } catch (error) {
            if (inputEl) {
                inputEl.style.borderColor = '#ef4444';
                inputEl.focus();
            }
            return;
        }

        // Process the URL based on item type
        if (this.currentUrlLinkItemType === 'todo') {
            const todo = this.todos.find(t => t.id === this.currentUrlLinkItemId);
            if (todo) {
                todo.url = url;
                todo.baseUrl = this.extractBaseUrl(url);
                this.showToast('URL added successfully! Todo moved to website-specific section.', 'success');
            }
        } else if (this.currentUrlLinkItemType === 'note') {
            const note = this.notes.find(n => n.id === this.currentUrlLinkItemId);
            if (note) {
                note.url = url;
                note.baseUrl = this.extractBaseUrl(url);
                this.showToast('URL added successfully! Note moved to website-specific section.', 'success');
            }
        }

        this.saveData();
        this.updateUI();
        this.closeUrlLinkModal();
    }

    saveEditedNote() {
        if (!this.currentEditingNoteId) return;

        const note = this.notes.find(n => n.id === this.currentEditingNoteId);
        if (!note) return;

        const titleEl = document.getElementById('edit-note-title');
        const contentEl = document.getElementById('edit-note-content');
        const urlEl = document.getElementById('edit-note-url');

        const newTitle = titleEl ? titleEl.value.trim() : '';
        const newContent = contentEl ? contentEl.value.trim() : '';
        const newUrl = urlEl ? urlEl.value.trim() : '';

        if (!newContent) {
            // Don't save if content is empty
            this.closeEditNoteModal();
            return;
        }

        // Update title and content
        note.title = newTitle || newContent.split('\n')[0].substring(0, 50);
        note.content = newContent;

        // Handle URL change for global notes
        if (note.url === 'global' && newUrl) {
            try {
                // Validate URL
                new URL(newUrl);
                note.url = newUrl;
            } catch (e) {
                // Invalid URL, show error and don't save
                if (urlEl) {
                    urlEl.style.borderColor = '#ef4444';
                    urlEl.focus();
                }
                return;
            }
        }

        this.saveData();
        this.updateUI();
        this.closeEditNoteModal();
    }

    linkToTodo(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const availableTodos = this.getFilteredTodos();
            if (availableTodos.length === 0) {
                alert('No todos available to link to. Create a todo first.');
                return;
            }

            const todoOptions = availableTodos.map(todo => `${todo.id}: ${todo.text}`).join('\n');
            const selectedTodoId = prompt(`Link to which todo?\n\n${todoOptions}\n\nEnter todo ID:`);

            if (selectedTodoId && availableTodos.find(t => t.id === selectedTodoId)) {
                note.linkedTodoId = selectedTodoId;
                this.saveData();
                this.updateUI();
                alert('Note linked to todo successfully!');
            }
        }
    }

    archiveNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            note.archived = true;
            note.archivedAt = new Date().toISOString();
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note archived', note.title || note.content.substring(0, 50), () => {
                const restoredNote = this.notes.find(n => n.id === noteId);
                if (restoredNote) {
                    restoredNote.archived = false;
                    delete restoredNote.archivedAt;
                    this.saveData();
                    this.updateUI();
                }
            });
        }
    }

    deleteNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            this.notes = this.notes.filter(n => n.id !== noteId);
            this.saveData();
            this.updateUI();

            this.showUndoToast('Note deleted', noteBackup.title || noteBackup.content.substring(0, 50), () => {
                this.notes.push(noteBackup);
                this.saveData();
                this.updateUI();
            });
        }
    }

    saveData() {
        chrome.storage.local.set({
            todos: this.todos,
            notes: this.notes,
            urlCache: this.urlCache
        });
    }

    saveSettings() {
        console.log('Saving settings:', this.settings);
        chrome.storage.local.set({
            settings: this.settings
        }).then(() => {
            console.log('Settings saved successfully');
        }).catch((error) => {
            console.error('Error saving settings:', error);
        });
    }

    formatDate(dateString) {
        if (!dateString) return '';

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString; // Return original if invalid date

        switch (this.settings.dateFormat) {
            case 'yyyy-mm-dd':
                return date.toISOString().split('T')[0];
            case 'dd-mm-yyyy':
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            case 'system':
            default:
                return date.toLocaleDateString();
        }
    }

    getImprovedDueDateGroup(dueDate) {
        if (!dueDate) return 'No Due Date';

        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const nextWeek = new Date(today);
        nextWeek.setDate(nextWeek.getDate() + 7);
        const due = new Date(dueDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        tomorrow.setHours(0, 0, 0, 0);
        nextWeek.setHours(0, 0, 0, 0);
        due.setHours(0, 0, 0, 0);

        if (due.getTime() === today.getTime()) {
            return 'Today';
        } else if (due.getTime() === tomorrow.getTime()) {
            return 'Tomorrow';
        } else if (due.getTime() > tomorrow.getTime() && due.getTime() <= nextWeek.getTime()) {
            return 'Soon';
        } else {
            return 'Later';
        }
    }

    getImprovedCreatedDateGroup(createdDate) {
        if (!createdDate) return 'No Date';

        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        const created = new Date(createdDate);

        // Reset time to compare dates only
        today.setHours(0, 0, 0, 0);
        yesterday.setHours(0, 0, 0, 0);
        weekAgo.setHours(0, 0, 0, 0);
        created.setHours(0, 0, 0, 0);

        if (created.getTime() === today.getTime()) {
            return 'Today';
        } else if (created.getTime() === yesterday.getTime()) {
            return 'Yesterday';
        } else if (created.getTime() > weekAgo.getTime() && created.getTime() < yesterday.getTime()) {
            return 'Recently';
        } else {
            return 'Ages ago 🦖';
        }
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // New UI methods
    showAddTodo() {
        // Ensure todos section is expanded
        const todosSection = document.querySelector('.todos-section');
        const todosContent = todosSection.querySelector('.section-content');
        if (todosContent) {
            todosContent.style.display = 'block';
            todosSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-todo-section').style.display = 'block';
        const todoInput = document.getElementById('todo-input');
        todoInput.focus();

        // Scroll to the form
        document.getElementById('add-todo-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddTodo() {
        document.getElementById('add-todo-section').style.display = 'none';
        document.getElementById('todo-input').value = '';
        document.getElementById('todo-due-date').style.display = 'none';
        document.getElementById('todo-due-date').value = '';

        // Reset per-item global mode
        this.todoGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    handleTodoEscape() {
        // ESC key should delete regardless of content
        this.hideAddTodo();
    }

    showAddNote() {
        // Ensure notes section is expanded
        const notesSection = document.querySelector('.notes-section');
        const notesContent = notesSection.querySelector('.section-content');
        if (notesContent) {
            notesContent.style.display = 'block';
            notesSection.classList.remove('collapsed');
        }

        // Show add form and focus input
        document.getElementById('add-note-section').style.display = 'block';
        const noteTitle = document.getElementById('note-title');
        noteTitle.focus();

        // Scroll to the form
        document.getElementById('add-note-section').scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    hideAddNote() {
        document.getElementById('add-note-section').style.display = 'none';
        document.getElementById('note-title').value = '';
        document.getElementById('note-content').value = '';

        // Reset per-item global mode
        this.noteGlobalMode = false;
        this.updateGlobalModeIcons();
        this.updatePlaceholders();
    }

    handleNoteEscape() {
        // ESC key should delete regardless of content
        this.hideAddNote();
    }

    addUrlToGlobalTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo || todo.url !== 'global') return;

        this.currentUrlLinkItemId = todoId;
        this.currentUrlLinkItemType = 'todo';
        this.showUrlLinkModal('Add URL to Todo', 'Enter URL to link this todo to:');
    }

    addUrlToGlobalNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (!note || note.url !== 'global') return;

        this.currentUrlLinkItemId = noteId;
        this.currentUrlLinkItemType = 'note';
        this.showUrlLinkModal('Add URL to Note', 'Enter URL to link this note to:');
    }

    convertTodoToGlobal(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (!todo || todo.url === 'global') return;

        // Store backup for undo
        const todoBackup = { ...todo };
        const removedUrl = todo.url;

        // Convert to global
        todo.url = 'global';
        delete todo.baseUrl;

        this.saveData();
        this.updateUI();

        // Show undo toast with removed URL
        this.showUndoToastWithUrl('Removed Todo Link', todo.text, removedUrl, () => {
            // Restore original todo
            const currentTodo = this.todos.find(t => t.id === todoId);
            if (currentTodo) {
                currentTodo.url = todoBackup.url;
                currentTodo.baseUrl = todoBackup.baseUrl;
                this.saveData();
                this.updateUI();
            }
        });
    }

    convertNoteToGlobal(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (!note || note.url === 'global') return;

        // Store backup for undo
        const noteBackup = { ...note };
        const removedUrl = note.url;

        // Convert to global
        note.url = 'global';
        delete note.baseUrl;

        this.saveData();
        this.updateUI();

        // Show undo toast with removed URL
        this.showUndoToastWithUrl('Removed Note Link', note.title || note.content.substring(0, 30) + '...', removedUrl, () => {
            // Restore original note
            const currentNote = this.notes.find(n => n.id === noteId);
            if (currentNote) {
                currentNote.url = noteBackup.url;
                currentNote.baseUrl = noteBackup.baseUrl;
                this.saveData();
                this.updateUI();
            }
        });
    }

    extractBaseUrl(url) {
        try {
            const urlObj = new URL(url);
            return `${urlObj.protocol}//${urlObj.hostname}`;
        } catch (error) {
            console.error('Error extracting base URL:', error);
            return url;
        }
    }

    async setTheme(theme) {
        // Save theme preference
        await chrome.storage.local.set({ theme: theme });

        // Apply theme to document
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.removeAttribute('data-theme');
        }

        console.log('Theme set to:', theme);
    }

    async loadTheme() {
        try {
            const result = await chrome.storage.local.get(['theme']);
            const theme = result.theme || 'light';

            // Apply theme to document
            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                document.documentElement.removeAttribute('data-theme');
            }

            // Update theme selector
            const themeSelect = document.getElementById('theme-select');
            if (themeSelect) {
                themeSelect.value = theme;
            }

            console.log('Theme loaded:', theme);
        } catch (error) {
            console.error('Error loading theme:', error);
        }
    }

    showDatePicker() {
        const todoDueDateEl = document.getElementById('todo-due-date');
        const datePickerInputEl = document.getElementById('date-picker-input');
        const modalEl = document.getElementById('date-picker-modal');

        const currentDate = todoDueDateEl ? todoDueDateEl.value : '';
        if (datePickerInputEl) {
            datePickerInputEl.value = currentDate;
        }
        if (modalEl) {
            modalEl.style.display = 'flex';
        }
    }

    setSelectedDate() {
        const datePickerInput = document.getElementById('date-picker-input');
        const selectedDate = datePickerInput ? datePickerInput.value : '';

        // If we're editing an existing todo
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = selectedDate || null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;
        } else {
            // For new todo creation
            const todoDueDateEl = document.getElementById('todo-due-date');

            if (todoDueDateEl) {
                todoDueDateEl.value = selectedDate;
                todoDueDateEl.style.display = selectedDate ? 'inline-block' : 'none';
            }

            // Return focus to the todo input after setting date
            const todoInputEl = document.getElementById('todo-input');
            if (todoInputEl) {
                setTimeout(() => {
                    todoInputEl.focus();
                }, 100); // Small delay to ensure modal is closed first
            }
        }

        const modalEl = document.getElementById('date-picker-modal');
        if (modalEl) {
            modalEl.style.display = 'none';
        }
    }

    clearSelectedDate() {
        // Clear the date picker input
        const datePickerInput = document.getElementById('date-picker-input');
        if (datePickerInput) {
            datePickerInput.value = '';
        }

        // If we're editing an existing todo, clear it immediately
        if (this.currentEditingTodoId) {
            const todo = this.todos.find(t => t.id === this.currentEditingTodoId);
            if (todo) {
                todo.dueDate = null;
                this.saveData();
                this.updateUI();
            }
            this.currentEditingTodoId = null;

            // Close modal after clearing existing todo date
            const modalEl = document.getElementById('date-picker-modal');
            if (modalEl) {
                modalEl.style.display = 'none';
            }
        } else {
            // For new todo creation, just clear the input but keep modal open
            const todoDueDateEl = document.getElementById('todo-due-date');
            if (todoDueDateEl) {
                todoDueDateEl.value = '';
                todoDueDateEl.style.display = 'none';
            }
        }
    }

    toggleSection(sectionName) {
        const content = document.getElementById(`${sectionName}-content`);
        const btn = document.querySelector(`[data-section="${sectionName}"] .collapse-btn`);

        if (!content || !btn) return;

        if (content.classList.contains('collapsed')) {
            content.classList.remove('collapsed');
            btn.classList.remove('collapsed');
            btn.textContent = '▼';
        } else {
            content.classList.add('collapsed');
            btn.classList.add('collapsed');
            btn.textContent = '▶';
        }
    }



    showUndoToast(message, itemText, undoCallback) {
        this.undoCallback = undoCallback;
        const titleEl = document.getElementById('undo-title');
        const itemTextEl = document.getElementById('undo-item-text');
        const toastEl = document.getElementById('undo-toast');
        const progressBarEl = document.getElementById('toast-progress-bar');

        // Set the title based on the message type
        if (titleEl) {
            if (message.includes('Todo')) {
                if (message.includes('deleted')) {
                    titleEl.textContent = 'ℹ Deleted Todo';
                } else if (message.includes('Restore')) {
                    titleEl.textContent = 'ℹ Restore Todo';
                } else {
                    titleEl.textContent = `ℹ ${message}`;
                }
            } else if (message.includes('Note')) {
                if (message.includes('archived')) {
                    titleEl.textContent = 'ℹ Archived Note';
                } else if (message.includes('deleted')) {
                    titleEl.textContent = 'ℹ Deleted Note';
                } else if (message.includes('Restore')) {
                    titleEl.textContent = 'ℹ Restore Note';
                } else {
                    titleEl.textContent = `ℹ ${message}`;
                }
            } else {
                titleEl.textContent = `ℹ ${message}`;
            }
        }

        // Set the item text
        if (itemTextEl) itemTextEl.textContent = itemText;
        if (toastEl) toastEl.style.display = 'block';

        // Reset progress bar
        if (progressBarEl) {
            progressBarEl.style.width = '100%';
        }

        // Clear any existing timeout
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start progress bar animation
        let timeLeft = 20000; // 20 seconds
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            timeLeft -= updateInterval;
            const percentage = (timeLeft / 20000) * 100;

            if (progressBarEl) {
                progressBarEl.style.width = `${Math.max(0, percentage)}%`;
            }

            if (timeLeft <= 0) {
                this.hideUndoToast();
            }
        }, updateInterval);

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    showUndoToastWithUrl(message, itemText, removedUrl, undoCallback) {
        this.undoCallback = undoCallback;
        const titleEl = document.getElementById('undo-title');
        const itemTextEl = document.getElementById('undo-item-text');
        const toastEl = document.getElementById('undo-toast');
        const progressBarEl = document.getElementById('toast-progress-bar');

        // Set the title
        if (titleEl) {
            titleEl.textContent = `ℹ ${message}`;
        }

        // Set the item text with URL on a new line
        if (itemTextEl) {
            itemTextEl.innerHTML = `${itemText}<br><span style="color: var(--text-muted); font-size: 12px;">🔗 ${removedUrl}</span>`;
        }

        if (toastEl) toastEl.style.display = 'block';

        // Reset progress bar
        if (progressBarEl) {
            progressBarEl.style.width = '100%';
        }

        // Clear any existing timeout
        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
        }

        // Start progress bar animation
        let timeLeft = 20000; // 20 seconds
        const updateInterval = 100; // Update every 100ms

        this.progressInterval = setInterval(() => {
            timeLeft -= updateInterval;
            const percentage = (timeLeft / 20000) * 100;

            if (progressBarEl) {
                progressBarEl.style.width = `${Math.max(0, percentage)}%`;
            }

            if (timeLeft <= 0) {
                this.hideUndoToast();
            }
        }, updateInterval);

        // Auto-hide after 20 seconds
        this.undoTimeout = setTimeout(() => {
            this.hideUndoToast();
        }, 20000);
    }

    hideUndoToast() {
        const toastEl = document.getElementById('undo-toast');
        if (toastEl) {
            toastEl.style.display = 'none';
        }

        if (this.undoTimeout) {
            clearTimeout(this.undoTimeout);
            this.undoTimeout = null;
        }

        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }

        this.undoCallback = null;
    }

    undoDelete() {
        if (this.undoCallback) {
            this.undoCallback();
            this.hideUndoToast();
        }
    }

    showNotification(message, type = 'info', duration = 3000) {
        // Create notification element if it doesn't exist
        let notification = document.getElementById('notification-toast');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'notification-toast';
            notification.className = 'notification-toast';
            document.body.appendChild(notification);
        }

        notification.textContent = message;
        notification.className = `notification-toast ${type}`;
        notification.style.display = 'block';

        // Auto-hide after duration
        setTimeout(() => {
            notification.style.display = 'none';
        }, duration);
    }

    updateCounts() {
        const filteredTodos = this.getFilteredTodos();
        const completedTodos = filteredTodos.filter(todo => todo.completed);
        const activeTodos = filteredTodos.filter(todo => !todo.completed);
        const filteredNotes = this.getFilteredNotes();

        document.getElementById('todos-count').textContent = `(${activeTodos.length})`;
        document.getElementById('notes-count').textContent = `(${filteredNotes.length})`;
    }

    setupCompletedPageScopeButtons() {
        // Handle scope buttons specifically for completed/archived page
        const completedPage = document.getElementById('completed-archived-page');
        if (completedPage) {
            const scopeButtons = completedPage.querySelectorAll('.scope-btn');
            scopeButtons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    // Update active state for completed page scope buttons
                    scopeButtons.forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');

                    this.completedScope = e.target.dataset.scope;
                    this.updateCompletedArchivedPage();
                });
            });
        }
    }

    showCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'none';
        if (completedPage) completedPage.style.display = 'block';

        this.updateCompletedArchivedPage();
    }

    hideCompletedArchivedPage() {
        const mainContent = document.querySelector('.content');
        const completedPage = document.getElementById('completed-archived-page');

        if (mainContent) mainContent.style.display = 'block';
        if (completedPage) completedPage.style.display = 'none';
    }

    getFilteredCompletedTodos() {
        const completedTodos = this.todos.filter(todo => todo.completed);

        switch (this.completedScope) {
            case 'global':
                return completedTodos.filter(todo => todo.url === 'global');
            case 'current-tab':
                return completedTodos.filter(todo => todo.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return completedTodos.filter(todo => {
                    try {
                        if (todo.url === 'global') return false;
                        const todoUrl = new URL(todo.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return todoUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
            default:
                return completedTodos;
        }
    }

    getFilteredArchivedNotes() {
        const archivedNotes = this.notes.filter(note => note.archived);

        switch (this.completedScope) {
            case 'global':
                return archivedNotes.filter(note => note.url === 'global');
            case 'current-tab':
                return archivedNotes.filter(note => note.url === this.currentTab?.url);
            case 'website':
                if (!this.currentTab?.baseUrl) return [];
                return archivedNotes.filter(note => {
                    try {
                        if (note.url === 'global') return false;
                        const noteUrl = new URL(note.url);
                        const currentBaseUrl = new URL(this.currentTab.baseUrl);
                        return noteUrl.hostname === currentBaseUrl.hostname;
                    } catch (error) {
                        return false;
                    }
                });
            case 'all':
            default:
                return archivedNotes;
        }
    }

    groupCompletedItems(items, itemType) {
        const groups = {};

        switch (this.completedGrouping) {
            case 'website':
                items.forEach(item => {
                    let groupName = 'Global';
                    if (item.url !== 'global') {
                        try {
                            const url = new URL(item.url);
                            groupName = url.hostname;
                        } catch (error) {
                            groupName = 'Unknown';
                        }
                    }
                    if (!groups[groupName]) groups[groupName] = [];
                    groups[groupName].push(item);
                });
                break;

            case 'completed-date':
                items.forEach(item => {
                    const date = itemType === 'todo' ? item.completedAt : item.archivedAt;
                    const groupName = date ? this.getImprovedCreatedDateGroup(date) : 'No Date';
                    if (!groups[groupName]) groups[groupName] = [];
                    groups[groupName].push(item);
                });
                break;

            case 'due-date':
                items.forEach(item => {
                    const groupName = this.getImprovedDueDateGroup(item.dueDate);
                    if (!groups[groupName]) groups[groupName] = [];
                    groups[groupName].push(item);
                });
                break;

            case 'created-date':
                items.forEach(item => {
                    const groupName = this.getImprovedCreatedDateGroup(item.createdAt);
                    if (!groups[groupName]) groups[groupName] = [];
                    groups[groupName].push(item);
                });
                break;

            case 'type':
                items.forEach(item => {
                    const groupName = item.url === 'global' ? 'Global' : 'Website-specific';
                    if (!groups[groupName]) groups[groupName] = [];
                    groups[groupName].push(item);
                });
                break;



            default:
                groups['All'] = items;
        }

        // Sort groups for improved date grouping
        if (this.completedGrouping === 'due-date') {
            const sortedGroups = {};
            const groupOrder = ['Today', 'Tomorrow', 'Soon', 'Later', 'No Due Date'];

            groupOrder.forEach(groupName => {
                if (groups[groupName]) {
                    // Sort items within group by due date
                    groups[groupName].sort((a, b) => {
                        if (!a.dueDate && !b.dueDate) return 0;
                        if (!a.dueDate) return 1;
                        if (!b.dueDate) return -1;
                        return new Date(a.dueDate) - new Date(b.dueDate);
                    });
                    sortedGroups[groupName] = groups[groupName];
                }
            });

            return sortedGroups;
        }

        if (this.completedGrouping === 'created-date') {
            const sortedGroups = {};
            const groupOrder = ['Today', 'Yesterday', 'Recently', 'Ages ago 🦖', 'No Date'];

            groupOrder.forEach(groupName => {
                if (groups[groupName]) {
                    // Sort items within group by created date (newest first)
                    groups[groupName].sort((a, b) => {
                        if (!a.createdAt && !b.createdAt) return 0;
                        if (!a.createdAt) return 1;
                        if (!b.createdAt) return -1;
                        return new Date(b.createdAt) - new Date(a.createdAt);
                    });
                    sortedGroups[groupName] = groups[groupName];
                }
            });

            return sortedGroups;
        }

        if (this.completedGrouping === 'completed-date') {
            const sortedGroups = {};
            const groupOrder = ['Today', 'Yesterday', 'Recently', 'Ages ago 🦖', 'No Date'];

            groupOrder.forEach(groupName => {
                if (groups[groupName]) {
                    // Sort items within group by completed/archived date (newest first)
                    groups[groupName].sort((a, b) => {
                        const dateA = itemType === 'todo' ? a.completedAt : a.archivedAt;
                        const dateB = itemType === 'todo' ? b.completedAt : b.archivedAt;

                        if (!dateA && !dateB) return 0;
                        if (!dateA) return 1;
                        if (!dateB) return -1;
                        return new Date(dateB) - new Date(dateA);
                    });
                    sortedGroups[groupName] = groups[groupName];
                }
            });

            return sortedGroups;
        }

        return groups;
    }

    updateCompletedArchivedPage() {
        // Get filtered completed todos based on scope
        const filteredCompletedTodos = this.getFilteredCompletedTodos();
        const completedPageList = document.getElementById('completed-page-list');
        const completedPageCount = document.getElementById('completed-page-count');

        if (completedPageList) {
            completedPageList.innerHTML = '';

            // Group todos if needed
            const groupedTodos = this.groupCompletedItems(filteredCompletedTodos, 'todo');

            if (this.completedGrouping === 'none') {
                filteredCompletedTodos.forEach(todo => {
                    const todoEl = this.createCompletedTodoElement(todo);
                    completedPageList.appendChild(todoEl);
                });
            } else {
                // Render grouped todos
                Object.entries(groupedTodos).forEach(([groupName, todos]) => {
                    if (todos.length > 0) {
                        const groupHeader = document.createElement('div');
                        groupHeader.className = 'group-header';
                        groupHeader.textContent = groupName;
                        completedPageList.appendChild(groupHeader);

                        todos.forEach(todo => {
                            const todoEl = this.createCompletedTodoElement(todo);
                            completedPageList.appendChild(todoEl);
                        });
                    }
                });
            }
        }

        if (completedPageCount) {
            completedPageCount.textContent = `(${filteredCompletedTodos.length})`;
        }

        // Get filtered archived notes based on scope
        const filteredArchivedNotes = this.getFilteredArchivedNotes();
        const archivedPageList = document.getElementById('archived-page-list');
        const archivedPageCount = document.getElementById('archived-page-count');

        if (archivedPageList) {
            archivedPageList.innerHTML = '';

            // Group notes if needed
            const groupedNotes = this.groupCompletedItems(filteredArchivedNotes, 'note');

            if (this.completedGrouping === 'none') {
                filteredArchivedNotes.forEach(note => {
                    const noteEl = this.createArchivedNoteElement(note);
                    archivedPageList.appendChild(noteEl);
                });
            } else {
                // Render grouped notes
                Object.entries(groupedNotes).forEach(([groupName, notes]) => {
                    if (notes.length > 0) {
                        const groupHeader = document.createElement('div');
                        groupHeader.className = 'group-header';
                        groupHeader.textContent = groupName;
                        archivedPageList.appendChild(groupHeader);

                        notes.forEach(note => {
                            const noteEl = this.createArchivedNoteElement(note);
                            archivedPageList.appendChild(noteEl);
                        });
                    }
                });
            }
        }

        if (archivedPageCount) {
            archivedPageCount.textContent = `(${filteredArchivedNotes.length})`;
        }
    }

    createCompletedTodoElement(todo) {
        const todoEl = document.createElement('div');
        todoEl.className = 'item completed';

        const urlLink = todo.url !== 'global' ? this.formatUrl(todo.url) : '';
        const completedDate = todo.completedAt ? this.formatDate(todo.completedAt) : '';
        const faviconUrl = todo.url !== 'global' ? this.getFaviconUrl(todo.url) : null;

        todoEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${todo.url !== 'global' ? `<a href="${todo.url}" class="item-url-link url-link" target="_blank" title="${todo.url}">
                        ${faviconUrl ? `<img src="${faviconUrl}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}
                    ${todo.url === 'global' ? '🌐 ' : ''}<span class="todo-text">${todo.text}</span>
                    ${completedDate ? `<span class="completed-date"> - Completed ${completedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-todo-btn" data-todo-id="${todo.id}" title="Restore">↩️</button>
                <button class="delete-todo-btn" data-todo-id="${todo.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = todoEl.querySelector('.restore-todo-btn');
        restoreBtn.addEventListener('click', () => this.restoreCompletedTodo(todo.id));

        const deleteBtn = todoEl.querySelector('.delete-todo-btn');
        deleteBtn.addEventListener('click', () => this.deleteTodo(todo.id));

        return todoEl;
    }

    createArchivedNoteElement(note) {
        const noteEl = document.createElement('div');
        noteEl.className = 'item archived';

        const urlLink = note.url !== 'global' ? this.formatUrl(note.url) : '';
        const archivedDate = note.archivedAt ? this.formatDate(note.archivedAt) : '';
        const faviconUrl = note.url !== 'global' ? this.getFaviconUrl(note.url) : null;

        noteEl.innerHTML = `
            <div class="item-content">
                <div class="item-text">
                    ${note.url !== 'global' ? `<a href="${note.url}" class="item-url-link url-link" target="_blank" title="${note.url}">
                        ${faviconUrl ? `<img src="${faviconUrl}" alt="favicon" class="favicon" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                        <span class="fallback-icon" style="display:none;">🔗</span>` : '🔗'}
                    </a> ` : ''}
                    ${note.title ? `<strong>${note.url === 'global' ? '🌐 ' : ''}${note.title}</strong><br>` : ''}
                    ${note.url === 'global' && !note.title ? '🌐 ' : ''}${note.content.length > 100 ? note.content.substring(0, 100) + '...' : note.content}
                    ${archivedDate ? `<span class="archived-date"> - Archived ${archivedDate}</span>` : ''}
                </div>
            </div>
            <div class="item-actions">
                <button class="restore-note-btn" data-note-id="${note.id}" title="Restore">↩️</button>
                <button class="delete-note-btn" data-note-id="${note.id}" title="Delete">🗑️</button>
            </div>
        `;

        // Add event listeners
        const restoreBtn = noteEl.querySelector('.restore-note-btn');
        restoreBtn.addEventListener('click', () => this.restoreArchivedNote(note.id));

        const deleteBtn = noteEl.querySelector('.delete-note-btn');
        deleteBtn.addEventListener('click', () => this.deleteNote(note.id));

        return noteEl;
    }

    restoreCompletedTodo(todoId) {
        const todo = this.todos.find(t => t.id === todoId);
        if (todo) {
            const todoBackup = { ...todo };
            todo.completed = false;
            delete todo.completedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();

            this.showUndoToast('Restore Todo', todo.text, () => {
                const restoredTodo = this.todos.find(t => t.id === todoId);
                if (restoredTodo) {
                    restoredTodo.completed = todoBackup.completed;
                    restoredTodo.completedAt = todoBackup.completedAt;
                    this.saveData();
                    this.updateCompletedArchivedPage();
                    this.updateUI();
                }
            });
        }
    }

    restoreArchivedNote(noteId) {
        const note = this.notes.find(n => n.id === noteId);
        if (note) {
            const noteBackup = { ...note };
            note.archived = false;
            delete note.archivedAt;
            this.saveData();
            this.updateCompletedArchivedPage();
            this.updateUI();

            this.showUndoToast('Restore Note', note.title || note.content.substring(0, 50), () => {
                const restoredNote = this.notes.find(n => n.id === noteId);
                if (restoredNote) {
                    restoredNote.archived = noteBackup.archived;
                    restoredNote.archivedAt = noteBackup.archivedAt;
                    this.saveData();
                    this.updateCompletedArchivedPage();
                    this.updateUI();
                }
            });
        }
    }

    openFullscreen() {
        // Close previous mode if user wants to close previous mode
        if (this.settings.closePreviousMode) {
            if (document.body.classList.contains('sidebar-mode') || document.body.classList.contains('floating-mode')) {
                window.close();
            } else {
                // We're in popup mode, close it by sending message to background
                chrome.runtime.sendMessage({ action: 'closePreviousMode', newMode: 'fullscreen' });
            }
        }

        chrome.tabs.create({
            url: chrome.runtime.getURL('fullscreen.html')
        });
    }

    openPopupWindow() {
        // Close previous mode if user wants to close previous mode
        if (this.settings.closePreviousMode) {
            if (document.body.classList.contains('sidebar-mode') || document.body.classList.contains('floating-mode')) {
                window.close();
                // Continue to open popup window via background script
            }
        }

        // Use background script to handle popup window creation
        chrome.runtime.sendMessage({ action: 'openPopupWindow' });
    }

    openFloatingWindow() {
        // Close previous mode if user wants to close previous mode
        if (this.settings.closePreviousMode) {
            if (document.body.classList.contains('sidebar-mode')) {
                // Close sidebar
                window.close();
                // Continue to open floating window via background script
            } else if (!document.body.classList.contains('floating-mode')) {
                // We're in popup mode, close it by sending message to background
                chrome.runtime.sendMessage({ action: 'closePreviousMode', newMode: 'floating' });
            }
        }

        // Use background script to handle floating window creation (which limits to 1)
        chrome.runtime.sendMessage({ action: 'openFloatingWindow' });
    }

    initFullscreenMode() {
        // Check if we're in fullscreen mode
        if (!document.body.classList.contains('fullscreen')) {
            return;
        }

        // Set default modes for fullscreen
        this.currentScope = 'all';
        this.todoGlobalMode = true;  // Default to global creation
        this.noteGlobalMode = true;  // Default to global creation
        this.currentGrouping = 'none';
        this.completedGrouping = 'none';

        // Set up fullscreen-specific event listeners
        this.setupFullscreenEventListeners();

        // Load and display items
        this.loadData().then(() => {
            // Apply layout based on setting
            this.applyFullscreenLayout();

            // Update placeholders for fullscreen mode
            this.updateFullscreenPlaceholders();

            // Update all lists
            this.updateFullscreenUI();
        });
    }

    setupFullscreenEventListeners() {
        // Layout toggle
        const layoutToggleBtn = document.getElementById('layout-toggle-btn');
        if (layoutToggleBtn) {
            layoutToggleBtn.addEventListener('click', () => {
                this.toggleFullscreenLayout();
            });
        }

        // Tab navigation
        const activeTabBtn = document.getElementById('active-tab');
        const completedTabBtn = document.getElementById('completed-tab');

        if (activeTabBtn) {
            activeTabBtn.addEventListener('click', () => {
                this.showFullscreenView('active');
            });
        }

        if (completedTabBtn) {
            completedTabBtn.addEventListener('click', () => {
                this.showFullscreenView('completed');
            });
        }

        // Add item buttons
        const addTodoBtn = document.getElementById('add-todo-btn');
        const addNoteBtn = document.getElementById('add-note-btn');

        if (addTodoBtn) {
            addTodoBtn.addEventListener('click', () => {
                this.addTodo();
            });
        }

        if (addNoteBtn) {
            addNoteBtn.addEventListener('click', () => {
                this.addNote();
            });
        }

        // Grouping controls
        const todoGroupBy = document.getElementById('todo-group-by');
        const noteGroupBy = document.getElementById('note-group-by');
        const completedTodoGroupBy = document.getElementById('completed-todo-group-by');
        const archivedNoteGroupBy = document.getElementById('archived-note-group-by');

        if (todoGroupBy) {
            todoGroupBy.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;
                this.updateFullscreenTodosList();
            });
        }

        if (noteGroupBy) {
            noteGroupBy.addEventListener('change', (e) => {
                this.currentGrouping = e.target.value;
                this.updateFullscreenNotesList();
            });
        }

        if (completedTodoGroupBy) {
            completedTodoGroupBy.addEventListener('change', (e) => {
                this.completedGrouping = e.target.value;
                this.updateFullscreenCompletedTodosList();
            });
        }

        if (archivedNoteGroupBy) {
            archivedNoteGroupBy.addEventListener('change', (e) => {
                this.completedGrouping = e.target.value;
                this.updateFullscreenArchivedNotesList();
            });
        }

        // Settings button
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettingsModal();
            });
        }

        // Enter key handlers for inputs
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');

        if (todoInput) {
            todoInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.addTodo();
                }
            });
        }

        if (noteContent) {
            noteContent.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.ctrlKey) {
                    this.addNote();
                }
            });
        }

        // Set up all modal event listeners
        this.setupEventListeners();
    }

    applyFullscreenLayout() {
        const body = document.body;
        const layoutToggleBtn = document.getElementById('layout-toggle-btn');

        if (this.settings.fullscreenLayout === 'vertical') {
            body.classList.remove('horizontal-split');
            body.classList.add('vertical-split');
            if (layoutToggleBtn) layoutToggleBtn.textContent = 'Switch to Horizontal';
        } else {
            body.classList.remove('vertical-split');
            body.classList.add('horizontal-split');
            if (layoutToggleBtn) layoutToggleBtn.textContent = 'Switch to Vertical';
        }
    }

    toggleFullscreenLayout() {
        this.settings.fullscreenLayout = this.settings.fullscreenLayout === 'vertical' ? 'horizontal' : 'vertical';
        this.saveSettings();
        this.applyFullscreenLayout();
    }

    updateFullscreenPlaceholders() {
        const todoInput = document.getElementById('todo-input');
        const noteContent = document.getElementById('note-content');
        const noteTitle = document.getElementById('note-title');

        if (todoInput) {
            todoInput.placeholder = 'Add a global todo (accessible from any website)...';
        }

        if (noteTitle) {
            noteTitle.placeholder = 'Global note title (optional)...';
        }

        if (noteContent) {
            noteContent.placeholder = 'Add a global note (accessible from any website)...';
        }
    }

    showFullscreenView(view) {
        const activeView = document.getElementById('active-view');
        const completedView = document.getElementById('completed-view');
        const activeTab = document.getElementById('active-tab');
        const completedTab = document.getElementById('completed-tab');

        if (view === 'active') {
            if (activeView) activeView.style.display = 'flex';
            if (completedView) completedView.style.display = 'none';
            if (activeTab) activeTab.classList.add('active');
            if (completedTab) completedTab.classList.remove('active');
        } else {
            if (activeView) activeView.style.display = 'none';
            if (completedView) completedView.style.display = 'flex';
            if (activeTab) activeTab.classList.remove('active');
            if (completedTab) completedTab.classList.add('active');

            // Update completed/archived lists when switching to that view
            this.updateFullscreenCompletedTodosList();
            this.updateFullscreenArchivedNotesList();
        }
    }

    updateFullscreenUI() {
        this.updateFullscreenTodosList();
        this.updateFullscreenNotesList();
    }

    updateFullscreenTodosList() {
        const listEl = document.getElementById('todos-list');
        if (!listEl) return;

        const filteredTodos = this.todos.filter(todo => !todo.completed);
        listEl.innerHTML = '';

        if (filteredTodos.length === 0) {
            listEl.innerHTML = '<div class="empty-state">No todos yet. Add one above!</div>';
            return;
        }

        // Apply grouping if enabled
        if (this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredTodos, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;

                const groupContent = document.createElement('div');
                groupContent.className = 'group-content';

                groups[groupName].forEach(todo => {
                    const todoEl = this.createTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                listEl.appendChild(groupContainer);
            });
        } else {
            // No grouping - show all todos
            filteredTodos.forEach(todo => {
                const todoEl = this.createTodoElement(todo);
                listEl.appendChild(todoEl);
            });
        }
    }

    updateFullscreenNotesList() {
        const listEl = document.getElementById('notes-list');
        if (!listEl) return;

        const filteredNotes = this.notes.filter(note => !note.archived);
        listEl.innerHTML = '';

        if (filteredNotes.length === 0) {
            listEl.innerHTML = '<div class="empty-state">No notes yet. Add one above!</div>';
            return;
        }

        // Apply grouping if enabled
        if (this.currentGrouping !== 'none') {
            const groups = this.groupItems(filteredNotes, this.currentGrouping);

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;

                const groupContent = document.createElement('div');
                groupContent.className = 'group-content';

                groups[groupName].forEach(note => {
                    const noteEl = this.createNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                listEl.appendChild(groupContainer);
            });
        } else {
            // No grouping - show all notes
            filteredNotes.forEach(note => {
                const noteEl = this.createNoteElement(note);
                listEl.appendChild(noteEl);
            });
        }
    }

    updateFullscreenCompletedTodosList() {
        const listEl = document.getElementById('completed-todos-list');
        if (!listEl) return;

        const completedTodos = this.todos.filter(todo => todo.completed);
        listEl.innerHTML = '';

        if (completedTodos.length === 0) {
            listEl.innerHTML = '<div class="empty-state">No completed todos yet.</div>';
            return;
        }

        // Apply grouping if enabled
        if (this.completedGrouping !== 'none') {
            const groups = this.groupCompletedItems(completedTodos, 'todo');

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;

                const groupContent = document.createElement('div');
                groupContent.className = 'group-content';

                groups[groupName].forEach(todo => {
                    const todoEl = this.createCompletedTodoElement(todo);
                    groupContent.appendChild(todoEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                listEl.appendChild(groupContainer);
            });
        } else {
            // No grouping - show all completed todos
            completedTodos.forEach(todo => {
                const todoEl = this.createCompletedTodoElement(todo);
                listEl.appendChild(todoEl);
            });
        }
    }

    updateFullscreenArchivedNotesList() {
        const listEl = document.getElementById('archived-notes-list');
        if (!listEl) return;

        const archivedNotes = this.notes.filter(note => note.archived);
        listEl.innerHTML = '';

        if (archivedNotes.length === 0) {
            listEl.innerHTML = '<div class="empty-state">No archived notes yet.</div>';
            return;
        }

        // Apply grouping if enabled
        if (this.completedGrouping !== 'none') {
            const groups = this.groupCompletedItems(archivedNotes, 'note');

            Object.keys(groups).forEach(groupName => {
                const groupContainer = document.createElement('div');
                groupContainer.className = 'group-container';

                const groupHeader = document.createElement('div');
                groupHeader.className = 'group-header';
                groupHeader.innerHTML = `<h4>${groupName} (${groups[groupName].length})</h4>`;

                const groupContent = document.createElement('div');
                groupContent.className = 'group-content';

                groups[groupName].forEach(note => {
                    const noteEl = this.createArchivedNoteElement(note);
                    groupContent.appendChild(noteEl);
                });

                groupContainer.appendChild(groupHeader);
                groupContainer.appendChild(groupContent);
                listEl.appendChild(groupContainer);
            });
        } else {
            // No grouping - show all archived notes
            archivedNotes.forEach(note => {
                const noteEl = this.createArchivedNoteElement(note);
                listEl.appendChild(noteEl);
            });
        }
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TodoNotesApp();
    // Make app globally available for inline event handlers
    window.app = app;
});
